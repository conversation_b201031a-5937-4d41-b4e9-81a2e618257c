import { resolveInt, resolveStr } from './utils';

export const enum NODE_ENV {
    DEV = 'development',
    PROD = 'production',
    TEST = 'test',
}

export interface AppConfig {
    port: number;
    nodeEnv: string;
    jwtSecret: string;
}

const nodeEnv = resolveStr('NODE_ENV', process.env.NODE_ENV);

// eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
export const isTest = nodeEnv === NODE_ENV.TEST;

export const appConfig: AppConfig = {
    port: resolveInt('APP_PORT', process.env.APP_PORT),
    nodeEnv,
    jwtSecret: resolveStr('JWT_SECRET', process.env.JWT_SECRET),
};
