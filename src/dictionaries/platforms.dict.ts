import { ClientEnum } from '@prisma/client';

export const PlatformDict = {
    [ClientEnum.PLATFORM]: {
        ru: 'Платформа',
        en: 'Platform',
        es: 'Plataforma',
        kk: 'Платформа',
        uz: 'Platforma',
        de: 'Plattform',
        fr: 'Plateforme',
        it: 'Piattaforma',
    },
    [ClientEnum.BROWSER]: {
        ru: 'Браузер',
        en: 'Browser',
        es: 'Navegador',
        kk: 'Браузер',
        uz: 'Brauzer',
        de: 'Browser',
        fr: 'Navigateur',
        it: 'Browser',
    },
    [ClientEnum.EMAIL]: {
        ru: 'Email',
        en: 'Email',
        es: 'Correo',
        kk: 'Пошта',
        uz: 'Pochta',
        de: 'Mail',
        fr: 'Mail',
        it: 'Email',
    },
    [ClientEnum.TELEGRAM]: {
        ru: 'Телеграм',
        en: 'Telegram',
        es: 'Telegram',
        kk: 'Телеграм',
        uz: 'Telegram',
        de: 'Telegram',
        fr: 'Telegram',
        it: 'Telegram',
    },
    [ClientEnum.MOBILE]: {
        ru: 'Приложение',
        en: 'Mobile',
        es: 'Móvil',
        kk: 'Мобилді',
        uz: 'Mobil',
        de: 'Mobil',
        fr: 'Mobile',
        it: 'Mobile',
    },
};
