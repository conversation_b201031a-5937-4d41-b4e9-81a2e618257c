import { Injectable, Logger, OnModuleD<PERSON>roy, OnModuleInit } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
    private readonly logger = new Logger(PrismaService.name);

    async onModuleInit() {
        try {
            await this.$connect();
            this.logger.log('Successfully connected to MongoDB');
        } catch (err) {
            this.logger.error('Unsuccessfully connected to MongoDB ', err);
        }
    }

    async onModuleDestroy() {
        await this.$disconnect();
    }
}
