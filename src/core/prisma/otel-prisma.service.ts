import { PrismaClient } from '@prisma/client';
import { tracePrismaQuery } from '@skillspace/tracing';

const prismaClient = new PrismaClient({
    // log: [{ level: 'query', emit: 'stdout' }],
});

export const OTEL_PRISMA_SERVICE = Symbol('OTEL_PRISMA');

export type OtelPrismaService = PrismaClient & {
    $extends: (arg: unknown) => PrismaClient;
};

export const aOtelPrismaService: OtelPrismaService = prismaClient.$extends({
    query: {
        $allModels: {
            async $allOperations({ model, operation, args, query }): Promise<unknown> {
                return tracePrismaQuery(model, operation, args, query);
            },
        },
    },
}) as OtelPrismaService;
