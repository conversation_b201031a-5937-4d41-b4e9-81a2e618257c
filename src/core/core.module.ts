import { ApolloServerPluginInlineTraceDisabled } from '@apollo/server/plugin/disabled';
import { ApolloFederationDriver, ApolloFederationDriverConfig } from '@nestjs/apollo';
import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_GUARD, Reflector } from '@nestjs/core';
import { CqrsModule } from '@nestjs/cqrs';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { GraphQLModule } from '@nestjs/graphql';
import { ScheduleModule } from '@nestjs/schedule';
import { AuthModule, JwtAuthGqlGuard, PermissionsGuard } from '@skillspace/access';
import {
    AmqpBrokerModule,
    AmqpExceptionFilter,
    AmqpInterceptor,
    RabbitMQExchangeConfig,
} from '@skillspace/amqp-contracts';
import {
    EMAIL_SERVICE_EXCHANGE,
    NOTIFICATIONS_EXCHANGE,
    TELEGRAM_BOT_EXCHANGE,
    WS_SENDER_EXCHANGE,
} from '@skillspace/amqp-contracts';
import { ExecutionContextRouterModule, HealthModule } from '@skillspace/common';
import { GqlInterceptor, GraphqlExceptionFilter } from '@skillspace/graphql';
import { LoggerModule } from '@skillspace/logger';
import { OpentelemetryModule } from '@skillspace/tracing';
import GraphQLJSON from 'graphql-type-json';

import { appConfig, NODE_ENV } from '../config/app.config';
import { PrismaModule } from './prisma/prisma.module';
import { PrismaService } from './prisma/prisma.service';

const AMQP_CONFIG_EXCHANGES: RabbitMQExchangeConfig[] = [
    EMAIL_SERVICE_EXCHANGE,
    TELEGRAM_BOT_EXCHANGE,
    WS_SENDER_EXCHANGE,
    NOTIFICATIONS_EXCHANGE,
];

@Global()
@Module({
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
            load: [() => ({ app: appConfig })],
        }),
        EventEmitterModule.forRoot(),
        CqrsModule,
        AuthModule.register({
            global: true,
            secret: appConfig.jwtSecret,
        }),
        GraphQLModule.forRoot<ApolloFederationDriverConfig>({
            driver: ApolloFederationDriver,
            // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
            playground: appConfig.nodeEnv !== NODE_ENV.PROD,
            autoSchemaFile: true,
            resolvers: { JSON: GraphQLJSON },
            formatError: (error) => {
                return {
                    message: error.message,
                    path: error.path,
                };
            },
            // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
            plugins: appConfig.nodeEnv === NODE_ENV.TEST ? [ApolloServerPluginInlineTraceDisabled()] : [],
        }),
        LoggerModule.forRoot(),
        AmqpBrokerModule.forRootAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => {
                return {
                    exchanges: AMQP_CONFIG_EXCHANGES,
                    mainExchange: NOTIFICATIONS_EXCHANGE,
                    uri: configService.get<string>('RMQ_URL'),
                };
            },
        }),
        AmqpBrokerModule,
        ExecutionContextRouterModule.register({
            gqlInterceptor: GqlInterceptor,
            amqpInterceptor: AmqpInterceptor,
            gqlFilter: GraphqlExceptionFilter,
            amqpFilter: AmqpExceptionFilter,
        }),
        HealthModule.register({
            prisma: PrismaService,
        }),
        PrismaModule,
        OpentelemetryModule.forRoot(),
        ScheduleModule.forRoot(),
    ],
    providers: [
        {
            provide: APP_GUARD,
            useClass: JwtAuthGqlGuard,
        },
        {
            provide: APP_GUARD,
            useClass: PermissionsGuard,
        },
        Reflector,
    ],
    exports: [AuthModule, PrismaModule, AmqpBrokerModule, CqrsModule],
})
export class CoreModule {}
