import '@skillspace/tracing';

import { NestFactory } from '@nestjs/core';
import { createLogger, LoggerService } from '@skillspace/logger';

import { AppModule } from './app.module';
import { setupGlobalMiddlewares } from './bootstrap-setup';
import { appConfig } from './config/app.config';

async function bootstrap() {
    const app = await NestFactory.create(AppModule, { logger: await createLogger() });
    const logger = app.get(LoggerService);

    setupGlobalMiddlewares(app);

    await app.listen(appConfig.port);
    logger.log(`GraphQL is running on ${appConfig.port} port`);
}

void bootstrap();
