import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { LocaleEnum } from '@prisma/client';
import { Request } from 'express';

const allowedLocales = Object.values(LocaleEnum);

export const Locale = createParamDecorator((_: unknown, ctx: ExecutionContext): string => {
    const context = GqlExecutionContext.create(ctx).getContext<{ req: Request }>();
    const { req: request } = context;

    const headers: Record<string, string> = {};
    if (request.rawHeaders && request.rawHeaders.length > 0) {
        for (let i = 0; i < request.rawHeaders.length; i += 2) {
            headers[request.rawHeaders[i].toLowerCase()] = request.rawHeaders[i + 1];
        }
    }

    let locale = headers.Locale || headers.locale || LocaleEnum.ru;

    // Удаляем часть после подчеркивания (например, из "en_USA" делаем "en")
    locale = locale.split('_')[0].toLowerCase();

    return allowedLocales.includes(locale as LocaleEnum) ? locale : LocaleEnum.ru;
});
