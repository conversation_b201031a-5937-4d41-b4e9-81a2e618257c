import { Inject, Logger } from '@nestjs/common';
import { <PERSON>Bus, <PERSON>Query, <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryH<PERSON><PERSON> } from '@nestjs/cqrs';
import { ClientEnum, LocaleEnum } from '@prisma/client';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';
import { PaginationDto } from '../../../shared/dto/pagination.dto';
import { UserUuid } from '../../domain/obj/user-uuid';
import { UserProvidedLocaleEvent } from '../events/user-provided-locale';
import { NotificationSchemaService } from '../services/notification-schema.service';
import { FindAllUserNotificationsType } from './types/find-all-user-notification.type';
import { UserNotificationResultType } from './types/user-notification-result.type';

export const getNotificationsPipeline = ({
    userUuid,
    schoolUuid,
    dto,
}: {
    userUuid: string;
    schoolUuid: string;
    dto: FindAllUserNotificationsType;
}) => {
    const matchConditions = {
        userUuid,
        clients: { $in: [ClientEnum.PLATFORM, 'platform'] },
        schoolUuid: { $in: [schoolUuid, null] },
    } as { userUuid: string; clients: { $in: string[] }; schoolUuid: { $in: string[] }; viewed?: boolean };

    if (dto.viewed !== undefined) {
        matchConditions.viewed = dto.viewed;
    }

    return [
        { $match: matchConditions },
        { $sort: { viewed: 1, createdAt: -1 } },
        {
            $lookup: {
                from: 'notifications',
                localField: 'notificationId',
                foreignField: '_id',
                as: 'notification',
                pipeline: [
                    {
                        $project: {
                            _id: 0,
                            meta: 1,
                            action: 1,
                        },
                    },
                ],
            },
        },
        { $unwind: '$notification' },
    ];
};

export class GetUserNotificationsQuery implements IQuery {
    constructor(
        public readonly params: {
            userUuid: string;
            schoolUuid: string;
            dto: FindAllUserNotificationsType;
            pagination: PaginationDto;
            locale: LocaleEnum;
        },
        public readonly currentDate: Date = new Date(),
    ) {}
}

@QueryHandler(GetUserNotificationsQuery)
export class GetUserNotificationsQH implements IQueryHandler<GetUserNotificationsQuery, UserNotificationResultType[]> {
    private readonly logger = new Logger(GetUserNotificationsQH.name);
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
        private schema: NotificationSchemaService,
        private readonly eventBus: EventBus,
    ) {}

    async execute(query: GetUserNotificationsQuery): Promise<UserNotificationResultType[]> {
        const { schoolUuid, locale } = query.params;
        const userUuid = UserUuid.create(query.params.userUuid).unwrap();
        const dto = query.params.dto;
        const pagination = query.params.pagination;

        // TODO: для синхронизации выбранного языка (убрать, когда появится сервис пользователей)
        this.eventBus.publish(new UserProvidedLocaleEvent({ userUuid, locale }));

        const aggregationPipeline = [
            ...getNotificationsPipeline({ userUuid, schoolUuid, dto }),
            { $skip: pagination?.skip || 0 },
            { $limit: pagination?.take || 30 },
            {
                $set: {
                    id: { $toString: '$_id' },
                    updatedAt: { $toLong: '$updatedAt' },
                    createdAt: { $toLong: '$createdAt' },
                    meta: '$notification.meta',
                    action: '$notification.action',
                },
            },
            {
                $project: {
                    _id: 0,
                    id: 1,
                    createdAt: 1,
                    updatedAt: 1,
                    userUuid: 1,
                    schoolUuid: 1,
                    viewed: 1,
                    group: 1,
                    action: 1,
                    meta: 1,
                },
            },
        ];
        const userNotifications = await this.prisma.userNotification.aggregateRaw({
            pipeline: aggregationPipeline,
        });

        const typedResult = userNotifications as unknown as UserNotificationResultType[];

        this.logger.verbose({ count: typedResult.length, locale }, 'Запрошен список уведомлений');

        return typedResult.map((notification) => {
            const meta =
                typeof notification.meta === 'string' ? (JSON.parse(notification.meta) as object) : notification.meta;
            const aSchema = this.schema.getMetaSchema(notification.action);
            const generatedText = new aSchema(meta, new Date(notification.createdAt)).getMessageText(locale);
            return {
                ...notification,
                text: generatedText,
                group: notification.group ? notification.group : this.schema.getGroupByAction(notification.action), // TODO: на 2 версии данных значение будет всегда
                meta,
            };
        });
    }
}
