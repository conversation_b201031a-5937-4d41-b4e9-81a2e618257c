import { Inject, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ClientEnum, NotificationGroupEnum } from '@prisma/client';

import { UserRoleEnum } from '../../../shared/enums/user.enum';
import { ISettingsRepository } from '../../domain/infrastructure/repositories/settings-repository.interface';
import { NotificationSettingsModel } from '../../domain/models/notification-settings.model';
import { UserUuid } from '../../domain/obj/user-uuid';
import { SETTINGS_REPOSITORY } from '../../injects';
import { NotificationSchemaService } from '../services/notification-schema.service';
import { NotificationsSettingsResultType } from './types/notification-settings-result.type';

export interface IGetNotificationSettingsParams {
    userUuid: string;
    role: UserRoleEnum;
}
export class GetNotificationSettingsQuery implements IQuery {
    constructor(public readonly params: IGetNotificationSettingsParams) {}
}

@QueryHandler(GetNotificationSettingsQuery)
export class GetNotificationSettingsQH implements IQueryHandler<GetNotificationSettingsQuery> {
    private readonly logger = new Logger(GetNotificationSettingsQH.name);
    constructor(
        @Inject(SETTINGS_REPOSITORY)
        private readonly settingsRepository: ISettingsRepository,
        private readonly schema: NotificationSchemaService,
    ) {}

    async execute(query: GetNotificationSettingsQuery): Promise<NotificationsSettingsResultType> {
        const { role } = query.params;
        const userUuid = UserUuid.create(query.params.userUuid).unwrap();
        const anUserSettings = await this.settingsRepository.findOne(userUuid);
        const settings = this.filterRoleGroups(anUserSettings, role);
        this.logger.verbose({ settings, userUuid }, 'Получение настроек уведомлений');
        return settings;
    }

    private filterRoleGroups(
        anUserSettings: NotificationSettingsModel,
        role: UserRoleEnum,
    ): NotificationsSettingsResultType {
        const groups = anUserSettings.settings;
        const uiGroups = {
            [UserRoleEnum.student]: this.schema.getStudentGroupCodes(),
            [UserRoleEnum.teacher]: this.schema.getTeacherGroupCodes(),
        }[role] as NotificationGroupEnum[];

        const result: Record<NotificationGroupEnum, ClientEnum[]> = {} as Record<NotificationGroupEnum, ClientEnum[]>;

        Object.entries(groups).forEach(([group, platforms]) => {
            if (uiGroups.includes(group as NotificationGroupEnum)) {
                result[group] = platforms;
            }
        });

        return {
            sound: anUserSettings.sound,
            ...result,
        };
    }
}
