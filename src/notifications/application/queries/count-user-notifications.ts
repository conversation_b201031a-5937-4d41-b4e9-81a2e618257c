import { Inject, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ActionEnum, NotificationGroupEnum } from '@prisma/client';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';
import { UserUuid } from '../../domain/obj/user-uuid';
import { NotificationSchemaService } from '../services/notification-schema.service';
import { getNotificationsPipeline } from './get-user-notifications';

export interface CountRowType {
    group: NotificationGroupEnum;
    countAll: number;
    countUnread: number;
}

export interface ICountUserNotificationsParams {
    userUuid: string;
    schoolUuid: string;
}
export class CountUserNotificationsQuery implements IQuery {
    constructor(public readonly params: ICountUserNotificationsParams) {}
}

@QueryHandler(CountUserNotificationsQuery)
export class CountUserNotificationsQH implements IQueryHandler<CountUserNotificationsQuery> {
    private readonly logger = new Logger(CountUserNotificationsQH.name);
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
        private readonly schema: NotificationSchemaService,
    ) {}

    async execute(query: CountUserNotificationsQuery): Promise<CountRowType[]> {
        const { schoolUuid } = query.params;
        const userUuid = UserUuid.create(query.params.userUuid).unwrap();

        const rawData = await this.prisma.userNotification.aggregateRaw({
            pipeline: [
                ...getNotificationsPipeline({ userUuid, schoolUuid, dto: {} }),
                {
                    $group: {
                        _id: '$notification.action',
                        countAll: { $sum: 1 },
                        countUnread: { $sum: { $cond: [{ $eq: ['$viewed', false] }, 1, 0] } },
                    },
                },

                {
                    $project: {
                        _id: 0,
                        action: '$_id',
                        countAll: 1,
                        countUnread: 1,
                    },
                },
            ],
        });

        const data = rawData as unknown as {
            action: ActionEnum;
            countAll: number;
            countUnread: number;
        }[];

        // Создаем объект для подсчета метрик по группам
        const groupData = data.reduce(
            (acc, item) => {
                const group = this.schema.getGroupByAction(item.action);
                if (!acc[group]) {
                    acc[group] = { group, countAll: 0, countUnread: 0 };
                }
                acc[group].countAll += item.countAll;
                acc[group].countUnread += item.countUnread;
                return acc;
            },
            {} as Record<NotificationGroupEnum, CountRowType>,
        );

        // Преобразуем объект в массив
        const result = Object.values(groupData) as unknown as CountRowType[];
        this.logger.verbose({ counts: result, userUuid, schoolUuid }, 'Подсчет уведомлений');
        return result.sort((a, b) => a.group.localeCompare(b.group));
    }
}
