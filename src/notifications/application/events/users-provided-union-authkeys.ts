import { Inject, Logger } from '@nestjs/common';
import { EventsHandler, IEvent, IEventHandler } from '@nestjs/cqrs';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';

export class UsersProvidedUnionAuthKeysEvent implements IEvent {
    constructor(
        public readonly users: {
            userUuid: string;
            unionAuthKey: string;
        }[],
    ) {}
}

@EventsHandler(UsersProvidedUnionAuthKeysEvent)
export class UsersProvidedUnionAuthKeysEventHandler implements IEventHandler<UsersProvidedUnionAuthKeysEvent> {
    private readonly logger = new Logger(UsersProvidedUnionAuthKeysEventHandler.name);
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
    ) {}
    async handle(event: UsersProvidedUnionAuthKeysEvent) {
        await Promise.all(
            event.users.map(({ userUuid, unionAuthKey }) =>
                this.prisma.user.upsert({
                    where: { userUuid },
                    update: { unionAuthKey },
                    create: { userUuid, unionAuthKey },
                }),
            ),
        );
        this.logger.verbose({ users: event.users }, 'Пользователи обновили unionAuthKey');
    }
}
