import { Inject, Logger } from '@nestjs/common';
import { EventsHandler, IEvent, IEventHandler } from '@nestjs/cqrs';
import { LocaleEnum } from '@prisma/client';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';

export class UserProvidedLocaleEvent implements IEvent {
    constructor(
        public readonly params: {
            userUuid: string;
            locale: LocaleEnum;
        },
    ) {}
}

@EventsHandler(UserProvidedLocaleEvent)
export class UserProvidedLocaleEventHandler implements IEventHandler<UserProvidedLocaleEvent> {
    private readonly logger = new Logger(UserProvidedLocaleEventHandler.name);
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
    ) {}
    async handle(event: UserProvidedLocaleEvent) {
        const { userUuid, locale } = event.params;
        const existingUser = await this.prisma.user.findUnique({
            where: { userUuid },
        });

        if (existingUser) {
            await this.prisma.user.update({
                where: { userUuid },
                data: { locale },
            });
            this.logger.debug({ userUuid, locale }, 'Локаль пользователя обновлена');
        } else {
            await this.prisma.user.create({
                data: { userUuid, locale },
            });
            this.logger.debug({ userUuid, locale }, 'Создан новый пользователь с локалью');
        }
    }
}
