import { Inject, Logger } from '@nestjs/common';
import { CommandHandler, EventBus, ICommand, ICommandHandler } from '@nestjs/cqrs';
import { ActionEnum } from '@prisma/client';
import { NotificationsCreateNotificationV2ContractNamespace } from '@skillspace/amqp-contracts';
import { CustomError, ERROR_CODE } from '@skillspace/utils';
import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';

import { getValidationErrorMessages } from '../../../shared/custom-error';
import { IUserProfileProvider } from '../../domain/infrastructure/providers/user-profile-provider.interface';
import { IBrowserPublisher } from '../../domain/infrastructure/publishers/browser-publisher.interface';
import { IEmailPublisher } from '../../domain/infrastructure/publishers/email-publisher.interface';
import { ITelegramPublisher } from '../../domain/infrastructure/publishers/telegram-publisher.interface';
import { INotificationsRepository } from '../../domain/infrastructure/repositories/notifications-repository.interface';
import { ISettingsRepository } from '../../domain/infrastructure/repositories/settings-repository.interface';
import { IUserNotificationsRepository } from '../../domain/infrastructure/repositories/user-notifications-repository.interface';
import { NotificationSettingsModel } from '../../domain/models/notification-settings.model';
import { NotificationModelV2 } from '../../domain/models/notification-v2.model';
import {
    BROWSER_PUBLISHER,
    EMAIL_PUBLISHER,
    NOTIFICATIONS_REPOSITORY,
    SETTINGS_REPOSITORY,
    TELEGRAM_PUBLISHER,
    USER_NOTIFICATIONS_REPOSITORY,
    USER_PROFILE_PROVIDER,
} from '../../injects';
import { UsersProvidedUnionAuthKeysEvent } from '../events/users-provided-union-authkeys';
import { NotificationSchemaService } from '../services/notification-schema.service';

export class SendNotificationCommand implements ICommand {
    constructor(public readonly message: NotificationsCreateNotificationV2ContractNamespace.Message) {}
}

@CommandHandler(SendNotificationCommand)
export class SendNotificationCH implements ICommandHandler<SendNotificationCommand> {
    constructor(
        @Inject(NOTIFICATIONS_REPOSITORY)
        private readonly notificationsRepository: INotificationsRepository,
        @Inject(SETTINGS_REPOSITORY)
        private readonly settingsRepository: ISettingsRepository,
        @Inject(USER_NOTIFICATIONS_REPOSITORY)
        private readonly userNotificationRepository: IUserNotificationsRepository,
        @Inject(EMAIL_PUBLISHER)
        private readonly emailPublisher: IEmailPublisher,
        @Inject(TELEGRAM_PUBLISHER)
        private readonly telegramPublisher: ITelegramPublisher,
        @Inject(BROWSER_PUBLISHER)
        private readonly browserPublisher: IBrowserPublisher,
        private readonly schema: NotificationSchemaService,
        @Inject(USER_PROFILE_PROVIDER)
        private readonly userProfile: IUserProfileProvider,
        private eventBus: EventBus,
    ) {}

    private readonly logger = new Logger(SendNotificationCH.name);

    async execute(command: SendNotificationCommand) {
        const payload = plainToClass(
            NotificationsCreateNotificationV2ContractNamespace.RequestPayload,
            command.message.payload,
        );
        const action = payload.action as ActionEnum;
        const notificationName = this.schema.getMaps().actions[action];

        const errors = await validate(payload);
        const errorMessages = getValidationErrorMessages(errors);
        if (errorMessages.length) {
            throw new CustomError(`Ошибка валидации сообщения "${notificationName}"`, {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: errorMessages,
            });
        }

        const notificationGroup = this.schema.getGroupByAction(action);
        const isSystemNotification = this.schema.isSystemGroup(notificationGroup);

        let anUserSettings: NotificationSettingsModel[];
        if (!isSystemNotification) {
            const userUuids = payload.users.map((u) => u.uuid);
            anUserSettings = await this.settingsRepository.finMany({ userUuids });
        }

        const userLocales = await this.userProfile.findMany({ userUuids: payload.users.map((u) => u.uuid) });

        const aNotification = NotificationModelV2.create({
            schema: this.schema,
            payload: {
                school: payload?.school,
                users: payload.users,
                action,
                group: notificationGroup,
                meta: payload.meta,
            },
            anUserSettings: anUserSettings,
            userLocales,
            createdAt: new Date(),
        });

        // Логирование тестовой проверки валидации меты

        // const metaErrorMessages = aNotification.safeValidateMeta();
        // if (metaErrorMessages.length) {
        //     this.logger.warn(
        //         { messages: metaErrorMessages, action, meta: payload.meta },
        //         `Мета данные сообщения "${notificationName}"`,
        //     );
        // }

        // Сохраняем уведомления
        const userNotificationsToSave = aNotification.getUserNotificationsToSave();
        if (userNotificationsToSave.length) {
            await Promise.all([
                this.notificationsRepository.create(aNotification),
                this.userNotificationRepository.createMany(userNotificationsToSave),
            ]);
        }

        // Отправляем уведомления
        const telegramUserNotificationsToSend = aNotification.getTelegramNotifications();
        const platformUserNotificationsToSend = aNotification.getPlatformNotifications();
        const emailUserNotificationsToSend = aNotification.getEmailNotifications(this.schema);

        await Promise.all([
            ...telegramUserNotificationsToSend.map((message) => this.telegramPublisher.sendMessage(message)),
            ...platformUserNotificationsToSend.map((message) => this.browserPublisher.sendMessage(message)),
            ...emailUserNotificationsToSend.map((message) => this.emailPublisher.sendMessage(message)),
        ]);

        // Выводим лог
        const counts = {
            tg: telegramUserNotificationsToSend.length,
            email: emailUserNotificationsToSend.length,
            ws: platformUserNotificationsToSend.length,
        };
        const total = Object.values(counts).reduce((acc, value) => acc + value, 0);

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { meta, ...payloadWithoutMeta } = payload;
        this.logger.log(
            { ...counts, saved: userNotificationsToSave.length, title: notificationName, payload: payloadWithoutMeta },
            `Отправлено ${total} пользовательских уведомлений "${notificationName}"`,
        );

        // TODO: для синхронизации unionAuthKey (убрать, когда появится сервис пользователей)
        this.eventBus.publish(
            new UsersProvidedUnionAuthKeysEvent(
                payload.users.map(({ uuid, unionAuthKey }) => ({ userUuid: uuid, unionAuthKey })),
            ),
        );
    }
}
