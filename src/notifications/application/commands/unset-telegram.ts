import { Inject, Logger } from '@nestjs/common';
import { CommandHandler, ICommand, ICommandHandler } from '@nestjs/cqrs';

import { IUserProfileProvider } from '../../domain/infrastructure/providers/user-profile-provider.interface';
import { ISettingsRepository } from '../../domain/infrastructure/repositories/settings-repository.interface';
import { SETTINGS_REPOSITORY, USER_PROFILE_PROVIDER } from '../../injects';

export interface UnsetTelegramParams {
    unionAuthKey: string;
}
export class UnsetTelegramCommand implements ICommand {
    constructor(public readonly params: UnsetTelegramParams) {}
}

@CommandHandler(UnsetTelegramCommand)
export class UnsetTelegramCH implements ICommandHandler<UnsetTelegramCommand> {
    private readonly logger = new Logger(UnsetTelegramCH.name);
    constructor(
        @Inject(SETTINGS_REPOSITORY)
        private readonly settingsRepository: ISettingsRepository,
        @Inject(USER_PROFILE_PROVIDER)
        private readonly userProfile: IUserProfileProvider,
    ) {}

    async execute(command: UnsetTelegramCommand): Promise<void> {
        const { unionAuthKey } = command.params;
        const profile = await this.userProfile.findByUnionAuthKey(unionAuthKey);
        const userUuid = profile.userUuid;
        const anNotificationSettings = await this.settingsRepository.findOne(userUuid);
        const anUpdatedNotificationSettings = anNotificationSettings.unsetTelegram();
        await this.settingsRepository.upsert(anUpdatedNotificationSettings);
        this.logger.log({ userUuid, unionAuthKey }, 'Telegram уведомления отключены');
    }
}
