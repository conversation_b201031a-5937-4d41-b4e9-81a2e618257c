import { ForbiddenException, Inject, Logger } from '@nestjs/common';
import { CommandHandler, ICommand, ICommandHandler } from '@nestjs/cqrs';
import { CustomError, ERROR_CODE } from '@skillspace/utils';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';
import { UserUuid } from '../../domain/obj/user-uuid';
import { UserNotificationsRepository } from '../../infrastructure/repositories/user-notifications.repository';
import { USER_NOTIFICATIONS_REPOSITORY } from '../../injects';

export interface MarkAsViewedParams {
    userUuid: string;
    id: string;
}
export class MarkAsViewedCommand implements ICommand {
    constructor(public readonly params: MarkAsViewedParams) {}
}

@CommandHandler(MarkAsViewedCommand)
export class Mark<PERSON><PERSON>iewedCH implements ICommandHandler<MarkAsViewedCommand> {
    private readonly logger = new Logger(MarkAsViewedCH.name);
    constructor(
        @Inject(USER_NOTIFICATIONS_REPOSITORY)
        private userNotificationsRepo: UserNotificationsRepository,
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
    ) {}

    async execute(command: MarkAsViewedCommand): Promise<void> {
        const userUuid = UserUuid.create(command.params.userUuid).unwrap();
        const notification = await this.prisma.userNotification.findUnique({ where: { id: command.params.id } });

        if (!notification) {
            throw new CustomError('Уведомление не найдено', {
                code: ERROR_CODE.NOT_FOUND_ERROR,
                details: { id: command.params.id, userUuid },
            });
        }

        if (notification?.userUuid !== userUuid) {
            throw new ForbiddenException();
        }
        await this.userNotificationsRepo.update(notification.id, { viewed: true });
        this.logger.verbose({ id: command.params.id, userUuid }, 'Уведомление отмечено как прочитанное');
    }
}
