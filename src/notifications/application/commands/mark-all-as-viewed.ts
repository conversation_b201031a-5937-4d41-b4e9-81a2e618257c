import { Inject, Logger } from '@nestjs/common';
import { CommandHandler, ICommand, ICommandHandler } from '@nestjs/cqrs';

import { UserUuid } from '../../domain/obj/user-uuid';
import { UserNotificationsRepository } from '../../infrastructure/repositories/user-notifications.repository';
import { USER_NOTIFICATIONS_REPOSITORY } from '../../injects';

export interface MarkAllAsViewedParams {
    userUuid: string;
    schoolId: string;
}
export class MarkAllAsViewedCommand implements ICommand {
    constructor(public readonly params: MarkAllAsViewedParams) {}
}

@CommandHandler(MarkAllAsViewedCommand)
export class MarkAllAsViewedCH implements ICommandHandler<MarkAllAsViewedCommand> {
    private readonly logger = new Logger(MarkAllAsViewedCH.name);
    constructor(
        @Inject(USER_NOTIFICATIONS_REPOSITORY)
        private userNotificationsRepo: UserNotificationsRepository,
    ) {}

    async execute(command: MarkAllAsViewedCommand): Promise<number> {
        const userUuid = UserUuid.create(command.params.userUuid).unwrap();
        const { schoolId } = command.params;
        const count = await this.userNotificationsRepo.markAllAsViewed(userUuid, schoolId);
        this.logger.verbose({ count, userUuid, schoolId }, 'Уведомления отмечены как прочитанные');
        return count;
    }
}
