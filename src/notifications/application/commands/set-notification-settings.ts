import { Inject, Logger } from '@nestjs/common';
import { CommandHandler, ICommand, ICommandHandler } from '@nestjs/cqrs';

import { ISettingsRepository } from '../../domain/infrastructure/repositories/settings-repository.interface';
import { DeliverySettings, NotificationSettingsModel } from '../../domain/models/notification-settings.model';
import { SETTINGS_REPOSITORY } from '../../injects';

export interface SetNotificationSettingsParams {
    userUuid: string;
    dto: DeliverySettings;
}

export class SetNotificationSettingsCommand implements ICommand {
    constructor(public readonly params: SetNotificationSettingsParams) {}
}

@CommandHandler(SetNotificationSettingsCommand)
export class SetNotificationSettingsCH implements ICommandHandler<SetNotificationSettingsCommand> {
    private readonly logger = new Logger(SetNotificationSettingsCH.name);
    constructor(
        @Inject(SETTINGS_REPOSITORY)
        private readonly settingsRepository: ISettingsRepository,
    ) {}

    async execute(command: SetNotificationSettingsCommand): Promise<NotificationSettingsModel> {
        const { userUuid, dto } = command.params;
        const anUserSettings = await this.settingsRepository.findOne(userUuid);
        const anUpdatedUserSettings = anUserSettings.update(dto);
        await this.settingsRepository.upsert(anUpdatedUserSettings);
        this.logger.verbose({ settings: anUpdatedUserSettings.settings, userUuid }, 'Настройки уведомлений обновлены');
        return anUpdatedUserSettings;
    }
}
