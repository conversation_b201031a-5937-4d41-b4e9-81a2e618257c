import { Injectable } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import {
    NotificationsCreateNotificationV2Contract,
    NotificationsCreateNotificationV2ContractNamespace,
} from '@skillspace/amqp-contracts';
import { AmqpSubscribe, RabbitPayload } from '@skillspace/amqp-contracts';

import { SendNotificationCommand } from '../application/commands/send-notification';

@Injectable()
export class NotificationsConsumer {
    constructor(private readonly commandBus: CommandBus) {}

    @AmqpSubscribe(NotificationsCreateNotificationV2Contract)
    async handleNotificationsMessageV2(
        @RabbitPayload() message: NotificationsCreateNotificationV2ContractNamespace.Message,
    ): Promise<void> {
        await this.commandBus.execute(new SendNotificationCommand(message));
    }
}
