import { Injectable } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import {
    NotificationsUnsetTelegramContract,
    NotificationsUnsetTelegramContractNamespace,
} from '@skillspace/amqp-contracts';
import { AmqpSubscribe, RabbitPayload } from '@skillspace/amqp-contracts';

import { UnsetTelegramCommand } from '../application/commands/unset-telegram';

@Injectable()
export class SettingsConsumer {
    constructor(private readonly commandBus: CommandBus) {}

    @AmqpSubscribe(NotificationsUnsetTelegramContract)
    async unsetTelegram(@RabbitPayload() dto: NotificationsUnsetTelegramContractNamespace.Message): Promise<void> {
        const command = new UnsetTelegramCommand({ unionAuthKey: dto.payload.unionAuthKey });
        await this.commandBus.execute(command);
    }
}
