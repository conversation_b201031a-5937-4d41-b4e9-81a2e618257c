import { CustomScalar, Scalar } from '@nestjs/graphql';
import { ClientEnum } from '@prisma/client';
import { Kind, ValueNode } from 'graphql';

@Scalar('NotificationClientsLang')
export class NotificationClientsLangScalar
    implements CustomScalar<Record<ClientEnum, string>, Record<ClientEnum, string>>
{
    description = 'Мапа клиентов [CLIENT_NAME]: "текст на русском"';

    parseValue(value: Record<ClientEnum, string>): Record<ClientEnum, string> {
        // Получает входное значение от клиента
        return value;
    }

    serialize(value: Record<ClientEnum, string>): Record<ClientEnum, string> {
        // Возвращает значение клиенту
        return value;
    }

    parseLiteral(ast: ValueNode): Record<ClientEnum, string> {
        // Обрабатывает значение из запроса
        if (ast.kind === Kind.STRING) {
            return ast.value as unknown as Record<ClientEnum, string>;
        }
        throw new Error('NotificationClientsLang must be a string');
    }
}
