import { Field, ObjectType, registerEnumType } from '@nestjs/graphql';
import { ClientEnum, NotificationGroupEnum, NotificationSoundEnum } from '@prisma/client';

import { NotificationsSettingsResultType } from '../../application/queries/types/notification-settings-result.type';

export enum RoleEnum {
    student = 'student',
    teacher = 'teacher',
}

registerEnumType(RoleEnum, { name: 'RoleEnum' });
registerEnumType(ClientEnum, { name: 'ClientEnum' });

registerEnumType(NotificationSoundEnum, { name: 'NotificationSoundEnum' });
registerEnumType(NotificationGroupEnum, { name: 'NotificationGroupEnum' });

@ObjectType()
export class NotificationsSettingsOutput implements NotificationsSettingsResultType {
    @Field(() => NotificationSoundEnum, { nullable: true })
    sound: NotificationSoundEnum | null;

    @Field(() => [ClientEnum], { nullable: true })
    [NotificationGroupEnum.COURSES]: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    [NotificationGroupEnum.GROUP_STUDENT_LIMIT_OVER]: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    [NotificationGroupEnum.COURSE_STUDENT_FINISHED]: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    [NotificationGroupEnum.WEBINARS]: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    [NotificationGroupEnum.HOMEWORKS]: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    [NotificationGroupEnum.DEADLINES]: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    [NotificationGroupEnum.AFFILIATE_UPDATES]: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    [NotificationGroupEnum.SCHOOL_SETTINGS]: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    [NotificationGroupEnum.CHAT]: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    [NotificationGroupEnum.BILLING]: ClientEnum[];
}
