import { Field, GraphQLISODateTime, ID, ObjectType, registerEnumType } from '@nestjs/graphql';
import { ActionEnum, NotificationGroupEnum } from '@prisma/client';

import { UserNotificationResultType } from '../../application/queries/types/user-notification-result.type';
import { NotificationMetaOutput } from './notification-meta.output';

registerEnumType(ActionEnum, { name: 'ActionEnum' });

@ObjectType()
export class UserNotificationOutput implements UserNotificationResultType {
    @Field(() => ID)
    id: string;

    @Field(() => ActionEnum)
    action: ActionEnum;

    @Field()
    viewed: boolean;

    @Field()
    schoolUuid: string;

    @Field(() => NotificationGroupEnum, { nullable: true })
    group: NotificationGroupEnum;

    @Field()
    text: string;

    @Field(() => NotificationMetaOutput)
    meta: NotificationMetaOutput;

    @Field(() => GraphQLISODateTime)
    createdAt: Date;

    @Field(() => GraphQLISODateTime)
    updatedAt: Date;
}
