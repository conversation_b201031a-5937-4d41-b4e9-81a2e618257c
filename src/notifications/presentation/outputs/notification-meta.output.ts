import { Field, ObjectType } from '@nestjs/graphql';
import { NotificationsCreateNotificationV2ContractNamespace } from '@skillspace/amqp-contracts';

@ObjectType()
class NotificationMetaModelBase {
    @Field()
    id: number;

    @Field()
    name: string;

    @Field()
    uuid: string;

    @Field({ nullable: true })
    avatar: string | null;
}

@ObjectType()
class NotificationMetaGroup extends NotificationMetaModelBase {
    @Field({ nullable: true })
    limit: number;
}

@ObjectType()
class NotificationMetaTransaction {
    @Field()
    id: number;

    @Field()
    createAt: number;

    @Field()
    amount: number;
}

@ObjectType()
class NotificationMetaPromoCode {
    @Field()
    code: string;

    @Field()
    id: number;

    @Field()
    uuid: string;

    @Field({ nullable: true })
    activeFrom: number;

    @Field({ nullable: true })
    activeTo: number;
}

@ObjectType()
class NotificationMetaWebinar {
    @Field()
    startAt: number;

    @Field({ nullable: true }) // TODO: убрать через контракт
    url: string;

    @Field({ nullable: true }) // TODO: убрать через контракт
    timezoneAbbr: string;
}

@ObjectType()
class NotificationMetaRequisites {
    @Field()
    createAt: number;
}

@ObjectType()
class NotificationMetaHomeworkUser {
    @Field()
    id: number;

    @Field()
    uuid: string;
}

@ObjectType()
class PartnerLevel {
    @Field()
    uuid: string;

    @Field()
    slug: string;

    @Field()
    name: string;
}

@ObjectType()
class NotificationMetaPartner {
    @Field()
    id: number;

    @Field()
    uuid: string;

    @Field()
    level: PartnerLevel;

    @Field()
    totalEarned: number;

    @Field()
    withdraw: number;
}

@ObjectType()
class MetaPartnerSchool extends NotificationMetaModelBase {
    @Field()
    url: string;

    @Field()
    tariffName: string;
}

@ObjectType()
class NotificationMetaReferralPayment {
    @Field()
    paymentDate: number;

    @Field()
    sum: number;

    @Field()
    commission: number;

    @Field()
    commissionSum: number;

    @Field(() => MetaPartnerSchool, { nullable: true })
    school: MetaPartnerSchool;
}

@ObjectType()
class WhiteLabel implements NotificationsCreateNotificationV2ContractNamespace.WhiteLabelMetaModel {
    @Field({ nullable: true })
    primaryColor: string;

    @Field({ nullable: true })
    hideMobileAppLinks: boolean;

    @Field({ nullable: true })
    isWhiteLabel: boolean;

    @Field({ nullable: true })
    schoolLogoUrl: string;

    @Field({ nullable: true })
    textOnly: boolean;
}

@ObjectType()
export class NotificationMetaOutput implements NotificationsCreateNotificationV2ContractNamespace.Meta {
    @Field(() => WhiteLabel, { nullable: true })
    whiteLabel: WhiteLabel;

    @Field(() => NotificationMetaModelBase, { nullable: true })
    course: NotificationMetaModelBase;

    @Field(() => NotificationMetaModelBase, { nullable: true })
    step: NotificationMetaModelBase;

    @Field(() => NotificationMetaModelBase, { nullable: true })
    student: NotificationMetaModelBase;

    @Field(() => NotificationMetaModelBase, { nullable: true })
    teacher: NotificationMetaModelBase;

    @Field(() => NotificationMetaModelBase, { nullable: true })
    lesson: NotificationMetaModelBase;

    @Field(() => NotificationMetaModelBase, { nullable: true })
    school: NotificationMetaModelBase;

    @Field(() => NotificationMetaGroup, { nullable: true })
    group: NotificationMetaGroup;

    @Field(() => NotificationMetaTransaction, { nullable: true })
    transaction: NotificationMetaTransaction;

    @Field(() => NotificationMetaPromoCode, { nullable: true })
    promoCode: NotificationMetaPromoCode;

    @Field(() => NotificationMetaWebinar, { nullable: true })
    webinar: NotificationMetaWebinar;

    @Field(() => NotificationMetaRequisites, { nullable: true })
    requisites: NotificationMetaRequisites;

    @Field(() => NotificationMetaPartner, { nullable: true })
    partner: NotificationMetaPartner;

    @Field(() => NotificationMetaReferralPayment, { nullable: true })
    referralPayment: NotificationMetaReferralPayment;

    @Field(() => NotificationMetaHomeworkUser, { nullable: true })
    homeworkUser: NotificationMetaHomeworkUser;

    @Field({ nullable: true })
    url: string;

    @Field({ nullable: true })
    domain: string;
}
