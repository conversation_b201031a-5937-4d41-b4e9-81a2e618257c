import { Field, ObjectType } from '@nestjs/graphql';
import { ActionEnum, ClientEnum, NotificationGroupEnum } from '@prisma/client';
import GraphQLJSON from 'graphql-type-json';

@ObjectType()
export class NotificationMapOutput {
    @Field(() => GraphQLJSON, { description: 'Словарь событий. <ActionEnum, Название>' })
    actions: Record<ActionEnum, string>;

    @Field(() => GraphQLJSON, {
        description: 'Словарь платформ для уведомлений:  <ClientEnum, Название>',
    })
    clients: Record<ClientEnum, string>;

    @Field(() => GraphQLJSON, {
        description: 'Словарь названий групп настроек:  <NotificationGroupEnum, Название>',
    })
    settings: Record<NotificationGroupEnum, string>;

    @Field(() => GraphQLJSON, {
        description: 'Словарь меток для групп настроек:  <NotificationGroupEnum, Название>',
    })
    labels: Record<NotificationGroupEnum, string>;
}
