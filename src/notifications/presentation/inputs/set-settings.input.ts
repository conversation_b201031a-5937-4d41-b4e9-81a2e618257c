import { Field, InputType } from '@nestjs/graphql';
import { ClientEnum, NotificationGroupEnum, NotificationSoundEnum } from '@prisma/client';
import { IsEnum, IsOptional } from 'class-validator';

@InputType()
export class SetSettingsInput {
    @Field(() => NotificationSoundEnum, { nullable: true })
    @IsOptional()
    @IsEnum(NotificationSoundEnum)
    sound?: NotificationSoundEnum;

    @Field(() => [ClientEnum], { nullable: true })
    @IsOptional()
    @IsEnum(ClientEnum, { each: true })
    [NotificationGroupEnum.COURSES]?: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    @IsOptional()
    @IsEnum(ClientEnum, { each: true })
    [NotificationGroupEnum.GROUP_STUDENT_LIMIT_OVER]?: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    @IsOptional()
    @IsEnum(ClientEnum, { each: true })
    [NotificationGroupEnum.COURSE_STUDENT_FINISHED]?: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    @IsOptional()
    @IsEnum(ClientEnum, { each: true })
    [NotificationGroupEnum.WEBINARS]?: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    @IsOptional()
    @IsEnum(ClientEnum, { each: true })
    [NotificationGroupEnum.HOMEWORKS]?: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    @IsOptional()
    @IsEnum(ClientEnum, { each: true })
    [NotificationGroupEnum.DEADLINES]?: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    @IsOptional()
    @IsEnum(ClientEnum, { each: true })
    [NotificationGroupEnum.AFFILIATE_UPDATES]?: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    @IsOptional()
    @IsEnum(ClientEnum, { each: true })
    [NotificationGroupEnum.SCHOOL_SETTINGS]?: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    @IsOptional()
    @IsEnum(ClientEnum, { each: true })
    [NotificationGroupEnum.CHAT]?: ClientEnum[];

    @Field(() => [ClientEnum], { nullable: true })
    @IsOptional()
    @IsEnum(ClientEnum, { each: true })
    [NotificationGroupEnum.BILLING]?: ClientEnum[];
}
