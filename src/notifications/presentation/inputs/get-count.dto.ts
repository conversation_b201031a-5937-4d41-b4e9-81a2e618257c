import { Field, Int, ObjectType } from '@nestjs/graphql';
import { NotificationGroupEnum } from '@prisma/client';

import { CountRowType } from '../../application/queries/count-user-notifications';

@ObjectType()
export class CountRow implements CountRowType {
    @Field(() => NotificationGroupEnum)
    group: NotificationGroupEnum;

    @Field(() => Int, { nullable: true })
    countAll: number;

    @Field(() => Int, { nullable: true })
    countUnread: number;
}
