import { Logger } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { LocaleEnum } from '@prisma/client';
import { GqlSchoolId, GqlUserId } from '@skillspace/access';
import { Args, ID, Mutation, Query, Resolver } from '@skillspace/graphql';

import { Locale } from '../../shared/decorators/locale.decorator';
import { PaginationDto } from '../../shared/dto/pagination.dto';
import { ParsePaginationPipe } from '../../shared/pipes/parse-pagination.pipe';
import { MarkAllAsViewedCommand } from '../application/commands/mark-all-as-viewed';
import { MarkAsUnViewedCommand } from '../application/commands/mark-as-unviewed';
import { MarkAsViewedCommand } from '../application/commands/mark-as-viewed';
import { CountUserNotificationsQuery } from '../application/queries/count-user-notifications';
import { GetUserNotificationsQuery } from '../application/queries/get-user-notifications';
import { FindAllUserNotificationsDto } from './inputs/find-all-user-notifications.dto';
import { CountRow } from './inputs/get-count.dto';
import { UserNotificationOutput } from './outputs/user-notification.output';
import { VoidResponse } from './outputs/void-response.output';

@Resolver(() => UserNotificationOutput)
export class UserNotificationsResolver {
    private readonly logger = new Logger(UserNotificationsResolver.name);
    constructor(
        private readonly commandBus: CommandBus,
        private readonly queryBus: QueryBus,
    ) {}

    @Query(() => [UserNotificationOutput])
    async notifications(
        @GqlUserId() userUuid: string,
        @GqlSchoolId() schoolUuid: string,
        @Locale() locale: LocaleEnum,
        @Args('dto', { type: () => FindAllUserNotificationsDto, nullable: true }) dto: FindAllUserNotificationsDto,
        @Args('pagination', { type: () => PaginationDto, nullable: true }, ParsePaginationPipe)
        pagination: PaginationDto,
        currentDate: Date = new Date(),
    ): Promise<UserNotificationOutput[]> {
        if (!userUuid) {
            this.logger.verbose('Не определен идентификатор пользователя при запросе уведомлений');
            return null;
        }
        const query = new GetUserNotificationsQuery({ userUuid, schoolUuid, dto, pagination, locale }, currentDate);
        return this.queryBus.execute(query);
    }

    @Query(() => [CountRow], { name: 'notificationsCount' })
    async getCount(@GqlUserId() userUuid: string, @GqlSchoolId() schoolUuid: string): Promise<CountRow[]> {
        if (!userUuid) {
            this.logger.verbose('Не определен идентификатор пользователя при запросе уведомлений');
            return null;
        }
        const query = new CountUserNotificationsQuery({ userUuid, schoolUuid });
        return await this.queryBus.execute(query);
    }

    @Mutation(() => Number, { name: 'readAll' })
    async markAllAsViewed(@GqlUserId() userUuid: string, @GqlSchoolId() schoolId: string): Promise<number> {
        const command = new MarkAllAsViewedCommand({ userUuid, schoolId });
        return await this.commandBus.execute(command);
    }

    @Mutation(() => VoidResponse, { name: 'markAsViewed' })
    async markAsViewed(
        @GqlUserId() userUuid: string,
        @Args('id', { type: () => ID }) id: string,
    ): Promise<VoidResponse> {
        const command = new MarkAsViewedCommand({ userUuid, id });
        await this.commandBus.execute(command);
        return { success: true };
    }

    @Mutation(() => VoidResponse, { name: 'markUnread' })
    async markUnread(@GqlUserId() userUuid: string, @Args('id', { type: () => ID }) id: string): Promise<VoidResponse> {
        const command = new MarkAsUnViewedCommand({ userUuid, id });
        await this.commandBus.execute(command);
        return { success: true };
    }
}
