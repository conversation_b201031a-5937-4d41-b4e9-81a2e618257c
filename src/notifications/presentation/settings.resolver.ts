import { Logger } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { LocaleEnum } from '@prisma/client';
import { GqlUserId } from '@skillspace/access';
import { Args, Mutation, Query, Resolver } from '@skillspace/graphql';

import { Locale } from '../../shared/decorators/locale.decorator';
import { UserRoleEnum } from '../../shared/enums/user.enum';
import { SetNotificationSettingsCommand } from '../application/commands/set-notification-settings';
import { GetNotificationSettingsQuery } from '../application/queries/get-notification-settings';
import { NotificationSchemaService } from '../application/services/notification-schema.service';
import { SetSettingsInput } from './inputs/set-settings.input';
import { NotificationMapOutput } from './outputs/notification-map.output';
import { NotificationsSettingsOutput, RoleEnum } from './outputs/notification-settings.output';

@Resolver(() => NotificationsSettingsOutput)
export class SettingsResolver {
    private readonly logger = new Logger(SettingsResolver.name);
    constructor(
        private readonly schema: NotificationSchemaService,
        private readonly commandBus: CommandBus,
        private readonly queryBus: QueryBus,
    ) {}

    @Query(() => NotificationsSettingsOutput, { name: 'notificationsSettings' })
    async getUserSettings(
        @GqlUserId() userUuid: string,
        @Args('role', { type: () => RoleEnum }) role: RoleEnum,
    ): Promise<NotificationsSettingsOutput> {
        if (!userUuid) {
            this.logger.verbose('Идентификатор пользователя не определен при запросе настроек');
            return null;
        }
        const query = new GetNotificationSettingsQuery({ userUuid, role: role as unknown as UserRoleEnum });
        return await this.queryBus.execute(query);
    }

    @Query(() => NotificationMapOutput, { name: 'notificationsMap' })
    getNotificationMap(@Locale() locale: LocaleEnum): NotificationMapOutput {
        return this.schema.getMaps(locale);
    }

    @Mutation(() => NotificationsSettingsOutput, { name: 'setNotificationsSettings' })
    async setUserSettings(
        @GqlUserId() userUuid: string,
        @Args('dto', { type: () => SetSettingsInput })
        dto: SetSettingsInput,
        @Args('role', { type: () => RoleEnum }) role: RoleEnum,
    ): Promise<NotificationsSettingsOutput> {
        const { sound, ...settings } = dto;
        const command = new SetNotificationSettingsCommand({ userUuid, dto: { sound, settings } });
        await this.commandBus.execute(command);
        const query = new GetNotificationSettingsQuery({ userUuid, role: role as unknown as UserRoleEnum });
        return await this.queryBus.execute(query);
    }
}
