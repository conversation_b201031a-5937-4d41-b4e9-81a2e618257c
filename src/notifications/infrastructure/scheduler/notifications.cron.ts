import { Inject, Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';

@Injectable()
export class NotificationsCron {
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
    ) {}

    private readonly logger = new Logger(NotificationsCron.name);

    @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
    async calculateOldNotifications(): Promise<void> {
        // проверка наличия старых уведомлений
        const allNotificationsCount = await this.prisma.userNotification.count();
        const v2Count = await this.prisma.userNotification.count({ where: { version: 2 } });
        const notPercentage = ((v2Count / allNotificationsCount) * 100).toFixed(0);
        this.logger.warn(
            { percentage: notPercentage, count: allNotificationsCount, v2: v2Count },
            `Использование новых уведомлений: ${notPercentage}%`,
        );

        // проверка наличия старых пользовательских настроек
        const allUserNotificationsCount = await this.prisma.notificationsSettings.count();
        const v2SettingsCount = await this.prisma.notificationsSettings.count({ where: { version: 2 } });
        const notSettingsPercentage = ((v2SettingsCount / allUserNotificationsCount) * 100).toFixed(0);
        this.logger.warn(
            { percentage: notSettingsPercentage, count: allUserNotificationsCount, v2: v2SettingsCount },
            `Использование новых настроек: ${notSettingsPercentage}%`,
        );
    }
}
