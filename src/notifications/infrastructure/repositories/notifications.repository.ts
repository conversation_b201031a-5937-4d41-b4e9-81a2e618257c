import { Inject, Injectable } from '@nestjs/common';
import { Notification } from '@prisma/client';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';
import { INotificationsRepository } from '../../domain/infrastructure/repositories/notifications-repository.interface';
import { NotificationModelV2 } from '../../domain/models/notification-v2.model';

@Injectable()
export class NotificationsRepository implements INotificationsRepository {
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
    ) {}

    public async create(model: NotificationModelV2): Promise<Notification> {
        return this.prisma.notification.create({
            data: model.notificationToSave,
        });
    }
}
