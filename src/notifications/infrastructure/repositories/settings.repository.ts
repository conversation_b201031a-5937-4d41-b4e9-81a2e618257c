import { Inject, Injectable } from '@nestjs/common';
import { ActionEnum, ClientEnum, NotificationGroupEnum, NotificationsSettings } from '@prisma/client';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';
import { NotificationSchemaService } from '../../application/services/notification-schema.service';
import { ISettingsRepository } from '../../domain/infrastructure/repositories/settings-repository.interface';
import { NotificationSettingsModel } from '../../domain/models/notification-settings.model';

@Injectable()
export class SettingsRepository implements ISettingsRepository {
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
        private readonly schema: NotificationSchemaService,
    ) {}

    public async upsert(settings: NotificationSettingsModel): Promise<void> {
        const { id, params } = settings;
        const data = await this.prisma.notificationsSettings.findUnique({ where: { id } });
        if (!data) {
            await this.create(settings);
            return;
        }
        if (data.version !== 2) {
            await this.removeUserSetting(id);
            await this.create(settings);
            return;
        }
        await this.prisma.notificationsSettings.update({ where: { id }, data: params });
        return;
    }

    public async findOne(userUuid: string): Promise<NotificationSettingsModel> {
        const data = await this.prisma.notificationsSettings.findUnique({
            where: { userUuid },
        });
        if (!data) {
            return NotificationSettingsModel.createDefault(userUuid, this.schema);
        }
        return this.mapToModel(data);
    }

    public async finMany(params: { userUuids: string[] }): Promise<NotificationSettingsModel[]> {
        const data = await this.prisma.notificationsSettings.findMany({
            where: { userUuid: { in: params.userUuids } },
        });
        return data.map((item) => this.mapToModel(item));
    }

    private mapToModel(data: NotificationsSettings): NotificationSettingsModel {
        const { id } = data;

        // на случай отсутствия настроек в базе, используем пустые массивы
        const resultSettings: Record<NotificationGroupEnum, ClientEnum[]> = this.schema
            .getCustomGroupCodes()
            .reduce((acc, group) => {
                acc[group] = [];
                return acc;
            }, {}) as Record<NotificationGroupEnum, ClientEnum[]>;

        // TODO: убрать этот блок после полного перехода на версию 2
        if (data.version !== 2) {
            const { platform, browser, mobile, telegram, email, ...rest } = data;

            const clientMap = {
                platform: ClientEnum.PLATFORM,
                browser: ClientEnum.BROWSER,
                mobile: ClientEnum.MOBILE,
                telegram: ClientEnum.TELEGRAM,
                email: ClientEnum.EMAIL,
            };

            const mappedToGroups = {
                platform: this.schema.mapActionsToGroups(platform as ActionEnum[]),
                browser: this.schema.mapActionsToGroups(browser as ActionEnum[]),
                mobile: this.schema.mapActionsToGroups(mobile as ActionEnum[]),
                telegram: this.schema.mapActionsToGroups(telegram as ActionEnum[]),
                email: this.schema.mapActionsToGroups(email as ActionEnum[]),
            };

            const targetPlatforms = Object.keys(mappedToGroups);

            for (const platform of targetPlatforms) {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                const mappedPlatform = clientMap[platform];
                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                const groups = mappedToGroups[platform];
                // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
                groups.forEach((group) => {
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                    if (resultSettings[group]) {
                        // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
                        resultSettings[group].push(mappedPlatform);
                    }
                });
            }

            // Удаление дубликатов из массивов в settings
            for (const key in resultSettings) {
                // eslint-disable-next-line no-prototype-builtins
                if (resultSettings.hasOwnProperty(key)) {
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
                    resultSettings[key] = [...new Set(resultSettings[key])];
                }
            }

            return NotificationSettingsModel.from(id, {
                ...rest,
                settings: resultSettings,
            });
        }

        const { version, userUuid, sound, settings } = data;

        for (const group in resultSettings) {
            // eslint-disable-next-line no-prototype-builtins
            if (settings.hasOwnProperty(group)) {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                resultSettings[group] = data.settings[group];
            }
        }

        return NotificationSettingsModel.from(id, {
            version,
            userUuid,
            sound,
            settings: resultSettings,
        });
    }

    private async create(settings: NotificationSettingsModel): Promise<void> {
        await this.prisma.notificationsSettings.create({
            data: {
                ...settings.params,
            },
        });
    }

    public async unsetTelegram(userUuid: string): Promise<void> {
        const userProfile = await this.prisma.notificationsSettings.findUnique({
            where: { userUuid },
        });

        if (!userProfile) {
            return;
        }

        await this.prisma.notificationsSettings.update({
            where: {
                id: userProfile.id,
            },
            data: {
                telegram: [],
            },
        });
    }

    private async removeUserSetting(id: string): Promise<void> {
        await this.prisma.notificationsSettings.delete({ where: { id } });
    }
}
