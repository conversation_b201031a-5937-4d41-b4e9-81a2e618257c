import { Inject, Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';
import { getNotificationsPipeline } from '../../application/queries/get-user-notifications';
import { IUserNotificationsRepository } from '../../domain/infrastructure/repositories/user-notifications-repository.interface';

@Injectable()
export class UserNotificationsRepository implements IUserNotificationsRepository {
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
    ) {}

    public async createMany(dto: Prisma.UserNotificationCreateManyInput[]): Promise<void> {
        await this.prisma.userNotification.createMany({
            data: dto,
        });
    }

    public async update(id: string, data: { viewed: boolean }): Promise<void> {
        await this.prisma.userNotification.update({
            where: { id },
            data: { viewed: data.viewed },
        });
    }

    public async markAllAsViewed(userUuid: string, schoolUuid: string): Promise<number> {
        const notifications = await this.prisma.userNotification.aggregateRaw({
            pipeline: [
                ...getNotificationsPipeline({ userUuid, schoolUuid, dto: { viewed: false } }),
                {
                    $set: {
                        id: { $toString: '$_id' },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        id: 1,
                    },
                },
            ],
        });

        const typedResult = notifications as unknown as { id: string }[];
        const notificationIds = typedResult.map((notification) => notification.id);

        if (notificationIds.length === 0) {
            return 0;
        }

        const result = await this.prisma.userNotification.updateMany({
            where: {
                id: { in: notificationIds },
            },
            data: { viewed: true },
        });

        return result.count;
    }
}
