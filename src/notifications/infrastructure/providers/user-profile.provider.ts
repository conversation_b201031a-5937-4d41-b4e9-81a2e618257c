import { Inject, Injectable } from '@nestjs/common';
import { LocaleEnum } from '@prisma/client';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';
import { IUserProfileProvider } from '../../domain/infrastructure/providers/user-profile-provider.interface';

export interface UserProfileData {
    userUuid: string;
    unionAuthKey: string;
    locale: LocaleEnum;
}

@Injectable()
export class UserProfileProvider implements IUserProfileProvider {
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
    ) {}

    public async findMany(params: { userUuids: string[] }): Promise<UserProfileData[]> {
        return this.prisma.user.findMany({ where: { userUuid: { in: params.userUuids } } });
    }

    public async findByUnionAuthKey(unionAuthKey: string): Promise<UserProfileData> {
        return this.prisma.user.findFirst({ where: { unionAuth<PERSON>ey } });
    }
}
