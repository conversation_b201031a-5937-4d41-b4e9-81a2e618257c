import { Injectable, Logger } from '@nestjs/common';
import {
    WebsocketNewNotificationContract,
    WebsocketNewNotificationContractNamespace,
} from '@skillspace/amqp-contracts';
import { AmqpManager } from '@skillspace/amqp-contracts';

import { IBrowserPublisher } from '../../domain/infrastructure/publishers/browser-publisher.interface';

@Injectable()
export class WsPublisher implements IBrowserPublisher {
    private readonly logger = new Logger(WsPublisher.name);
    constructor(private readonly amqpConnection: AmqpManager) {}

    async sendMessage(payload: WebsocketNewNotificationContractNamespace.RequestPayload): Promise<void> {
        this.logger.debug('Отправка сообщения в браузер: ' + payload.action);
        await this.amqpConnection.publish(WebsocketNewNotificationContract, payload);
    }
}
