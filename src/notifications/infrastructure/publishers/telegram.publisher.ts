import { Injectable, Logger } from '@nestjs/common';
import { TelegramBotSendMessageContract, TelegramBotSendMessageContractNamespace } from '@skillspace/amqp-contracts';
import { AmqpManager } from '@skillspace/amqp-contracts';

import { ITelegramPublisher } from '../../domain/infrastructure/publishers/telegram-publisher.interface';

@Injectable()
export class TelegramPublisher implements ITelegramPublisher {
    private readonly logger = new Logger(TelegramPublisher.name);
    constructor(private readonly amqpConnection: AmqpManager) {}

    async sendMessage(payload: TelegramBotSendMessageContractNamespace.RequestPayload): Promise<void> {
        this.logger.debug('Отправка сообщения в телеграмм: ' + payload.text);
        return this.amqpConnection.publish(TelegramBotSendMessageContract, payload);
    }
}
