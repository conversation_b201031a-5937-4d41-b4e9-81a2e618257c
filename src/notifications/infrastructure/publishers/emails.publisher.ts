import { Injectable, Logger } from '@nestjs/common';
import {
    EmailServiceSendEmailToManyContract,
    EmailServiceSendEmailToManyContractNamespace,
} from '@skillspace/amqp-contracts';
import { AmqpManager } from '@skillspace/amqp-contracts';

import { IEmailPublisher } from '../../domain/infrastructure/publishers/email-publisher.interface';

@Injectable()
export class EmailPublisher implements IEmailPublisher {
    private readonly logger = new Logger(EmailPublisher.name);
    constructor(private readonly amqpConnection: AmqpManager) {}

    async sendMessage(payload: EmailServiceSendEmailToManyContractNamespace.RequestPayload): Promise<void> {
        this.logger.debug('Отправка письма: ' + payload.subject);
        return this.amqpConnection.publish(EmailServiceSendEmailToManyContract, payload);
    }
}
