import { Wrapper } from '@skillspace/cqrs';
import { ObjectId } from 'bson';

export class Identifier extends Wrapper<string> {
    constructor(value: string) {
        super(value);
    }

    public static generate(): Identifier {
        const id = new ObjectId().toHexString();
        return Identifier.wrap(id);
    }

    public static wrap(value: string): Identifier {
        return new Identifier(value);
    }
}
