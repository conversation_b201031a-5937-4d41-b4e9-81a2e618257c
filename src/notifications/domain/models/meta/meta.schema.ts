import { z } from 'zod';

export const IsId = z.number().int().positive('ID должен быть положительным числом');
export const IsUuid = z.string().uuid('UUID должен быть в формате UUID v4');
export const NonEmptyString = z.string().min(1, 'Поле не может быть пустым');
export const PositiveNumber = z.number().positive('Число должно быть положительным');
export const OptionalUrl = z.string().url('Значение должно быть ссылкой').nullable().optional();

const MetaBaseSchema = z.object({
    id: z.number().int().positive('ID должен быть положительным числом'),
    uuid: z.string().uuid('UUID должен быть в формате UUID v4'),
    name: z.string().min(1, 'Название не может быть пустым'),
});

/**
 * Course
 */
export const CourseSchema = MetaBaseSchema;
export type CourseType = z.infer<typeof CourseSchema>;

/**
 * Category
 */
export const CategorySchema = MetaBaseSchema;
export type CategoryType = z.infer<typeof CategorySchema>;

/**
 * Lesson
 */
export const LessonSchema = MetaBaseSchema;
export type LessonType = z.infer<typeof LessonSchema>;

/**
 * Step
 */
export const StepSchema = MetaBaseSchema;
export type StepType = z.infer<typeof StepSchema>;

/**
 * Group
 */
export const GroupSchema = MetaBaseSchema.extend({
    limit: z.number().int().positive('Ограничение должно быть положительным числом').optional(),
});
export type GroupType = z.infer<typeof GroupSchema>;

/**
 * Student
 */
export const StudentSchema = MetaBaseSchema.extend({
    name: z.string().min(1, 'Имя не может быть пустым').optional(),
    avatar: OptionalUrl,
});
export type StudentType = z.infer<typeof StudentSchema>;

/**
 * Teacher
 */
export const TeacherSchema = MetaBaseSchema.extend({
    avatar: OptionalUrl,
});
export type TeacherType = z.infer<typeof TeacherSchema>;

/**
 * HomeworkUser
 */
export const HomeworkUserSchema = z.object({
    id: IsId,
    uuid: IsUuid,
});
export type HomeworkUserType = z.infer<typeof HomeworkUserSchema>;

/**
 * Webinar
 */
export const WebinarSchema = z.object({
    startAt: z.number().positive('Дата начала должна быть положительной'),
});
export type WebinarType = z.infer<typeof WebinarSchema>;

/**
 * WhiteLabel
 */
export const WhiteLabelSchema = z.object({
    primaryColor: z
        .string()
        .regex(/^#([0-9A-Fa-f]{3}){1,2}$/i, 'Некорректный формат цвета')
        .optional(),
    hideMobileAppLinks: z.boolean().optional(),
    isWhiteLabel: z.boolean().optional(),
    schoolLogoUrl: OptionalUrl,
    textOnly: z.boolean().optional(),
});
export type WhiteLabelType = z.infer<typeof WhiteLabelSchema>;

/**
 * Requisites
 */
export const RequisitesSchema = z.object({
    createAt: z.number().positive('Дата создания должна быть положительной'),
});
export type RequisitesType = z.infer<typeof RequisitesSchema>;

/**
 * Partner
 */
export const PartnerSchema = z.object({
    id: IsId,
    uuid: IsUuid,
    level: z.object({
        uuid: IsUuid,
        slug: z.string().min(1, 'Slug уровня партнерки не может быть пустым'),
        name: z.string().min(1, 'Название уровня партнерки не может быть пустым'),
    }),
    totalEarned: z.number(),
    withdraw: z.number(),
});
export type PartnerType = z.infer<typeof PartnerSchema>;

/**
 * Transaction
 */
export const TransactionSchema = z.object({
    id: IsId,
    amount: PositiveNumber,
    createAt: z.number().positive('Дата создания должна быть положительной'),
});
export type TransactionType = z.infer<typeof TransactionSchema>;

/**
 * School
 */
export const SchoolSchema = z.object({
    id: IsId,
    name: NonEmptyString,
    uuid: IsUuid,
    avatar: OptionalUrl,
});
export type SchoolType = z.infer<typeof SchoolSchema>;

/**
 * ReferralPayment
 */
export const ReferralPaymentSchema = z.object({
    paymentDate: z.number().positive('Дата платежа должна быть положительной'),
    sum: PositiveNumber,
    commission: z.number().min(0, 'Комиссия должна быть неотрицательной'),
    commissionSum: PositiveNumber,
    school: z.object({
        id: IsId,
        uuid: IsUuid,
        name: z.string().min(1, 'Название школы не может быть пустым'),
        avatar: OptionalUrl,
        url: z.string().url('Ссылка школы должны быть валидным URL'),
        tariffName: z.string().min(1, 'Название тарифа не может быть пустым'),
    }),
});
export type ReferralPaymentType = z.infer<typeof ReferralPaymentSchema>;

/**
 * PromoCode
 */
export const PromoCodeSchema = z.object({
    id: IsId,
    uuid: IsUuid,
    code: z.string().min(1, 'Код промокода не может быть пустым'),
    activeFrom: z.number().nullable(),
    activeTo: z.number().nullable(),
});
export type PromoCodeType = z.infer<typeof PromoCodeSchema>;

/**
 * Feature
 */

export enum FeatureCodeEnum {
    TOTAL_USER_LIMIT = 'totaluserlimit',
    STUDENT_LIMIT = 'studentlimit',
    STORAGE_LIMIT = 'storagelimit',
    EMPLOYEE_LIMIT = 'employeelimit',
    COURSE_LIMIT = 'courselimit',
}

export const FeatureSchema = z.object({
    id: IsId,
    name: z.string().min(1, 'Название функции не может быть пустым'),
    code: z.enum(['totaluserlimit', 'studentlimit', 'storagelimit', 'employeelimit', 'courselimit']),
    limit: z.number().int().positive('Лимит должен быть положительным целым числом'),
    usage_limit: z.number().int().positive('Ограничение использования должно быть положительным целым числом'),
    usage: z.number().int().nonnegative('Использование не может быть отрицательным'),
});
export type FeatureType = z.infer<typeof FeatureSchema>;

/**
 * MetaAbstract
 */
export const MetaAbstractSchema = z.object({
    whiteLabel: WhiteLabelSchema.optional(),
    domain: z.string().min(1, 'Домен не может быть пустым'),
    url: z.string().url('Ссылка для уведомления должна быть валидным URL'),
});
