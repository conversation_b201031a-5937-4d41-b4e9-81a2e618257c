import { LocaleEnum } from '@prisma/client';

import { DeadlineMeta, DeadlineMetaType } from '../abstract/deadline-meta.abstract';

export class UpcomingDeadline1DayMeta extends DeadlineMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { lesson } = this.meta as DeadlineMetaType;
        return {
            [LocaleEnum.ru]: `Остался 1 день на выполнение задания <b>${lesson.name}</b>`,
            [LocaleEnum.en]: `There is 1 day left to complete the task <b>${lesson.name}</b>`,
            [LocaleEnum.es]: `Queda 1 día para completar la tarea <b>${lesson.name}</b>`,
            [LocaleEnum.kk]: `Тапсырманы бітіруге 1 күн қалды <b>${lesson.name}</b>`,
            [LocaleEnum.uz]: `<PERSON><PERSON><PERSON><PERSON> bajarlash uchun 1 kundan keyin qoldi <b>${lesson.name}</b>`,
            [LocaleEnum.de]: `Es bleibt noch 1 Tag, um die Aufgabe <b>${lesson.name}</b> abzuschließen`,
            [LocaleEnum.fr]: `Il reste 1 jour pour terminer la tâche <b>${lesson.name}</b>`,
            [LocaleEnum.it]: `Rimane 1 giorno per completare l'attività <b>${lesson.name}</b>`,
        }[locale];
    }

    public getUrl(): string {
        return this.getLessonUrl();
    }
}
