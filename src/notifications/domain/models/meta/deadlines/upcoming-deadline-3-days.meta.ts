import { LocaleEnum } from '@prisma/client';

import { DeadlineMeta, DeadlineMetaType } from '../abstract/deadline-meta.abstract';

export class UpcomingDeadline3DaysMeta extends DeadlineMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { lesson } = this.meta as DeadlineMetaType;
        return {
            [LocaleEnum.ru]: `Остался 3 дня на прохождение задания <b>${lesson.name}</b>`,
            [LocaleEnum.en]: `There are 3 days left to complete the task <b>${lesson.name}</b>`,
            [LocaleEnum.es]: `Quedan 3 días para completar la tarea <b>${lesson.name}</b>`,
            [LocaleEnum.kk]: `Тапсырманы бітіруге 3 күн қалды <b>${lesson.name}</b>`,
            [LocaleEnum.uz]: `<PERSON>azi<PERSON><PERSON> bajarish uchun 3 kundan keyin qoldi <b>${lesson.name}</b>`,
            [LocaleEnum.de]: `Es bleiben noch 3 Tage, um die Aufgabe <b>${lesson.name}</b> abzuschließen`,
            [LocaleEnum.fr]: `Il reste 3 jours pour terminer la tâche <b>${lesson.name}</b>`,
            [LocaleEnum.it]: `Rimangono 3 giorni per completare l'attività <b>${lesson.name}</b>`,
        }[locale];
    }

    public getUrl(): string {
        return this.getLessonUrl();
    }
}
