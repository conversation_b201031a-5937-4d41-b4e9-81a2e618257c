import { LocaleEnum } from '@prisma/client';

import { DeadlineMeta, DeadlineMetaType } from '../abstract/deadline-meta.abstract';

export class DeadlineExpiredMeta extends DeadlineMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { lesson } = this.meta as DeadlineMetaType;
        return {
            [LocaleEnum.ru]: `Вы не успели выполнить задание<b>${lesson.name}</b>`,
            [LocaleEnum.en]: `You have not completed the task <b>${lesson.name}</b>`,
            [LocaleEnum.es]: `No has completado la tarea <b>${lesson.name}</b> a tiempo`,
            [LocaleEnum.kk]: `Сіз <b>${lesson.name}</b> тапсырмасын бітіруге уақыттыңыз жетпейді`,
            [LocaleEnum.uz]: `<PERSON><PERSON> vazifani vaqtinchalik bajaramadingiz <b>${lesson.name}</b>`,
            [LocaleEnum.de]: `Sie haben die Aufgabe <b>${lesson.name}</b> nicht rechtzeitig abgeschlossen`,
            [LocaleEnum.fr]: `Vous n'avez pas eu le temps de terminer la tâche <b>${lesson.name}</b>`,
            [LocaleEnum.it]: `Non hai avuto il tempo di completare l'attività <b>${lesson.name}</b>`,
        }[locale];
    }

    public getUrl(): string {
        return this.getLessonUrl();
    }
}
