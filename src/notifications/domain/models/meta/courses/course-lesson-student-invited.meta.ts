import { LocaleEnum } from '@prisma/client';

import { CourseLessonMeta, CourseLessonMetaType } from '../abstract/course-lesson-meta.abstract';

export class CourseLessonStudentInvitedMeta extends CourseLessonMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { course, lesson } = this.meta as CourseLessonMetaType;
        return {
            [LocaleEnum.ru]: `Открыт доступ к занятию <b>"${lesson.name}"</b> в курсе <b>"${course.name}"</b>`,
            [LocaleEnum.en]: `Open access to the lesson <b>"${lesson.name}"</b> in the course <b>"${course.name}"</b>`,
            [LocaleEnum.es]: `Se ha abierto el acceso a la lección <b>"${lesson.name}"</b> en el curso <b>"${course.name}"</b>`,
            [LocaleEnum.kk]: `Курс <b>"${course.name}"</b> ішіндегі сабаққа <b>"${lesson.name}"</b> кіру мүмкіндігі ашық`,
            [LocaleEnum.uz]: `Kurs <b>"${course.name}"</b> ichidagi darsga <b>"${lesson.name}"</b> kirish ochilgan`,
            [LocaleEnum.de]: `Zugriff auf die Lektion <b>"${lesson.name}"</b> im Kurs <b>"${course.name}"</b> freigeschaltet`,
            [LocaleEnum.fr]: `Accès ouvert à la leçon <b>"${lesson.name}"</b> dans le cours <b>"${course.name}"</b>`,
            [LocaleEnum.it]: `Accesso aperto alla lezione <b>"${lesson.name}"</b> nel corso <b>"${course.name}"</b>`,
        }[locale];
    }

    public getUrl(): string {
        return this.getCourseUrl();
    }
}
