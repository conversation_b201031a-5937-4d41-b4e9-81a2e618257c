import { LocaleEnum } from '@prisma/client';

import { CourseAccessMeta, CourseAccessMetaType } from '../abstract/course-access-meta.abstract';
import { UserEmailParam } from '../abstract/notification-meta.abstract';

export class CourseStudentInvited extends CourseAccessMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getEmailTitle(userParams: UserEmailParam): string {
        const { course, courses, categories } = this.meta as CourseAccessMetaType;
        const { locale } = userParams;

        // Если определен один курс
        if (course) {
            return {
                [LocaleEnum.ru]: 'Новый курс доступен для прохождения в вашем личном кабинете',
                [LocaleEnum.en]: 'A new course is available for you to complete in your personal account',
                [LocaleEnum.es]: 'Un nuevo curso está disponible para ti para completar en tu cuenta personal',
                [LocaleEnum.kk]: 'Сізге жаңа курс ашық',
                [LocaleEnum.uz]: 'Sizga yangi kurs ochiq',
                [LocaleEnum.de]: 'Ein neuer Kurs ist für Sie in Ihrem persönlichen Konto verfügbar',
                [LocaleEnum.fr]: 'Un nouveau cours est disponible pour vous dans votre compte personnel',
                [LocaleEnum.it]: 'Un nuovo corso è disponibile per te nel tuo account personale',
            }[locale];
        }

        // Если определены категории или курсы (множественное число)
        if ((categories && categories.length > 0) || (courses && courses.length > 0)) {
            return {
                [LocaleEnum.ru]: 'Новые курсы доступны для прохождения в вашем личном кабинете',
                [LocaleEnum.en]: 'New courses are available for you to complete in your personal account',
                [LocaleEnum.es]: 'Nuevos cursos están disponibles para ti para completar en tu cuenta personal',
                [LocaleEnum.kk]: 'Сізге жаңа курстар ашық',
                [LocaleEnum.uz]: 'Sizga yangi kurslar ochiq',
                [LocaleEnum.de]: 'Neue Kurse sind für Sie in Ihrem persönlichen Konto verfügbar',
                [LocaleEnum.fr]: 'De nouveaux cours sont disponibles pour vous dans votre compte personnel',
                [LocaleEnum.it]: 'Nuovi corsi sono disponibili per te nel tuo account personale',
            }[locale];
        }

        // Fallback к одному курсу
        return {
            [LocaleEnum.ru]: 'Новый курс доступен для прохождения в вашем личном кабинете',
            [LocaleEnum.en]: 'A new course is available for you to complete in your personal account',
            [LocaleEnum.es]: 'Un nuevo curso está disponible para ti para completar en tu cuenta personal',
            [LocaleEnum.kk]: 'Сізге жаңа курс ашық',
            [LocaleEnum.uz]: 'Sizga yangi kurs ochiq',
            [LocaleEnum.de]: 'Ein neuer Kurs ist für Sie in Ihrem persönlichen Konto verfügbar',
            [LocaleEnum.fr]: 'Un nouveau cours est disponible pour vous dans votre compte personnel',
            [LocaleEnum.it]: 'Un nuovo corso è disponibile per te nel tuo account personale',
        }[locale];
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { course, courses, categories } = this.meta as CourseAccessMetaType;

        // Если определен один курс
        if (course) {
            return {
                [LocaleEnum.ru]: `Вам открыт доступ к курсу &laquo;${course.name}&raquo;.`,
                [LocaleEnum.en]: `You have been granted access to the course &laquo;${course.name}&raquo;.`,
                [LocaleEnum.es]: `Se te ha concedido acceso al curso &laquo;${course.name}&raquo;.`,
                [LocaleEnum.kk]: `Сізге &laquo;${course.name}&raquo; курсына кіру мүмкіндігі берілді.`,
                [LocaleEnum.uz]: `Sizga &laquo;${course.name}&raquo; kursiga kirish huquqi berildi.`,
                [LocaleEnum.de]: `Sie haben Zugriff auf den Kurs &laquo;${course.name}&raquo; erhalten.`,
                [LocaleEnum.fr]: `Vous avez reçu un accès au cours &laquo;${course.name}&raquo;.`,
                [LocaleEnum.it]: `Ti è stato concesso l'accesso al corso &laquo;${course.name}&raquo;.`,
            }[locale];
        }

        // Если определены категории
        if (categories && categories.length > 0) {
            const categoryNames = categories
                .map((category) => `&laquo;${category.name}&raquo;`)
                .join(', ')
                .replace(/,([^,]*)$/, '.');
            return {
                [LocaleEnum.ru]: `Вам открыт доступ к курсам из категорий ${categoryNames}`,
                [LocaleEnum.en]: `You have been granted access to courses from categories ${categoryNames}`,
                [LocaleEnum.es]: `Se te ha concedido acceso a cursos de las categorías ${categoryNames}`,
                [LocaleEnum.kk]: `Сізге ${categoryNames} категорияларынан курстарға кіру мүмкіндігі берілді`,
                [LocaleEnum.uz]: `Sizga ${categoryNames} kategoriyalaridan kurslarga kirish huquqi berildi`,
                [LocaleEnum.de]: `Sie haben Zugriff auf Kurse aus den Kategorien ${categoryNames} erhalten`,
                [LocaleEnum.fr]: `Vous avez reçu un accès aux cours des catégories ${categoryNames}`,
                [LocaleEnum.it]: `Ti è stato concesso l'accesso ai corsi delle categorie ${categoryNames}`,
            }[locale];
        }

        // Если определены курсы
        if (courses && courses.length > 0) {
            const courseNames = courses
                .map((course) => `&laquo;${course.name}&raquo;`)
                .join(', ')
                .replace(/,([^,]*)$/, '.');
            return {
                [LocaleEnum.ru]: `Вам открыт доступ к курсам ${courseNames}`,
                [LocaleEnum.en]: `You have been granted access to courses ${courseNames}`,
                [LocaleEnum.es]: `Se te ha concedido acceso a los cursos ${courseNames}`,
                [LocaleEnum.kk]: `Сізге ${courseNames} курстарына кіру мүмкіндігі берілді`,
                [LocaleEnum.uz]: `Sizga ${courseNames} kurslariga kirish huquqi berildi`,
                [LocaleEnum.de]: `Sie haben Zugriff auf die Kurse ${courseNames} erhalten`,
                [LocaleEnum.fr]: `Vous avez reçu un accès aux cours ${courseNames}`,
                [LocaleEnum.it]: `Ti è stato concesso l'accesso ai corsi ${courseNames}`,
            }[locale];
        }

        // Fallback - возвращаем пустую строку, если ничего не определено
        return '';
    }

    public getEmailText(userParams: UserEmailParam): string {
        const { locale } = userParams;
        const messageText = this.getMessageText(locale);

        if (!messageText) {
            return '';
        }

        const loginPrompt = {
            [LocaleEnum.ru]: '<br> Чтобы начать обучение, войдите в аккаунт:',
            [LocaleEnum.en]: '<br> To start learning, log in to your account:',
            [LocaleEnum.es]: '<br> Para comenzar a aprender, inicia sesión en tu cuenta:',
            [LocaleEnum.kk]: '<br> Оқуды бастау үшін, аккаунтқа кіріңіз:',
            [LocaleEnum.uz]: "<br> O'rganishni boshlash uchun, hisobingizga kiring:",
            [LocaleEnum.de]: '<br> Um mit dem Lernen zu beginnen, melden Sie sich bei Ihrem Konto an:',
            [LocaleEnum.fr]: '<br> Pour commencer à apprendre, connectez-vous à votre compte:',
            [LocaleEnum.it]: '<br> Per iniziare ad imparare, accedi al tuo account:',
        }[locale];

        return messageText + loginPrompt;
    }

    public getEmailButtonText(locale: LocaleEnum): string {
        return {
            [LocaleEnum.ru]: 'Войти в аккаунт',
            [LocaleEnum.en]: 'Log in to your account',
            [LocaleEnum.es]: 'Inicia sesión en tu cuenta',
            [LocaleEnum.kk]: 'Акаунтқа кіру',
            [LocaleEnum.uz]: 'Hisobingizga kiring',
            [LocaleEnum.de]: 'Anmelden bei Ihrem Konto',
            [LocaleEnum.fr]: 'Connectez-vous à votre compte',
            [LocaleEnum.it]: 'Accedi al tuo account',
        }[locale];
    }

    public getUrl(): string {
        return this.getCourseUrl();
    }
}
