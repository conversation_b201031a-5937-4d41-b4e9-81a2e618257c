import { LocaleEnum } from '@prisma/client';

import { CourseAccessMeta, CourseAccessMetaType } from '../abstract/course-access-meta.abstract';
import { UserEmailParam } from '../abstract/notification-meta.abstract';

export class CourseStudentInvited extends CourseAccessMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getEmailTitle(userParams: UserEmailParam): string {
        return {
            [LocaleEnum.ru]: 'Новый курс доступен для прохождения в вашем личном кабинете',
            [LocaleEnum.en]: 'A new course is available for you to complete in your personal account',
            [LocaleEnum.es]: 'Un nuevo curso está disponible para ti para completar en tu cuenta personal',
            [LocaleEnum.kk]: 'Сізге жаңа курс ашық',
            [LocaleEnum.uz]: 'Sizga yangi kurs ochiq',
            [LocaleEnum.de]: 'Ein neuer Kurs ist für Sie in Ihrem persönlichen Konto verfügbar',
            [LocaleEnum.fr]: 'Un nouveau cours est disponible pour vous dans votre compte personnel',
            [LocaleEnum.it]: 'Un nuovo corso è disponibile per te nel tuo account personale',
        }[userParams.locale];
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { course } = this.meta as CourseAccessMetaType;
        return {
            [LocaleEnum.ru]: `Вам открыт доступ к курсу <b>"${course.name}"</b><br> Чтобы начать обучение, войдите в аккаунт:`,
            [LocaleEnum.en]: `You have been invited to the course <b>"${course.name}"</b><br> To start learning, log in to your account:`,
            [LocaleEnum.es]: `Se te ha concedido acceso al curso <b>"${course.name}"</b><br> Para comenzar a aprender, inicia sesión en tu cuenta:`,
            [LocaleEnum.kk]: `Сізге курсқа <b>"${course.name}"</b> кіру мүмкіндігі ашық<br> Оқуды бастау үшін, аккаунтқа кіріңіз:`,
            [LocaleEnum.uz]: `Sizga kursga <b>"${course.name}"</b> kirish ochilgan<br> O'rganishni boshlash uchun, hisobingizga kiring:`,
            [LocaleEnum.de]: `Sie haben Zugriff auf den Kurs <b>"${course.name}"</b> erhalten<br> Um mit dem Lernen zu beginnen, melden Sie sich bei Ihrem Konto an:`,
            [LocaleEnum.fr]: `Vous avez reçu un accès au cours <b>"${course.name}"</b><br> Pour commencer à apprendre, connectez-vous à votre compte:`,
            [LocaleEnum.it]: `Ti è stato concesso l'accesso al corso <b>"${course.name}"</b><br> Per iniziare ad imparare, accedi al tuo account:`,
        }[locale];
    }

    public getEmailButtonText(locale: LocaleEnum): string {
        return {
            [LocaleEnum.ru]: 'Войти в аккаунт',
            [LocaleEnum.en]: 'Log in to your account',
            [LocaleEnum.es]: 'Inicia sesión en tu cuenta',
            [LocaleEnum.kk]: 'Акаунтқа кіру',
            [LocaleEnum.uz]: 'Hisobingizga kiring',
            [LocaleEnum.de]: 'Anmelden bei Ihrem Konto',
            [LocaleEnum.fr]: 'Connectez-vous à votre compte',
            [LocaleEnum.it]: 'Accedi al tuo account',
        }[locale];
    }

    public getUrl(): string {
        return this.getCourseUrl();
    }
}
