import { LocaleEnum } from '@prisma/client';

import { CourseLessonMeta, CourseLessonMetaType } from '../abstract/course-lesson-meta.abstract';

export class CourseNewLessonMeta extends CourseLessonMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { course, lesson } = this.meta as CourseLessonMetaType;
        return {
            [LocaleEnum.ru]: `В курсе <b>"${course.name}"</b> появилось новое занятие <b>"${lesson.name}"</b>`,
            [LocaleEnum.en]: `The lesson <b>"${lesson.name}"</b> in the course <b>"${course.name}"</b> is now available`,
            [LocaleEnum.es]: `Se ha agregado una nueva lección <b>"${lesson.name}"</b> al curso <b>"${course.name}"</b>`,
            [LocaleEnum.kk]: `Курсқа <b>"${course.name}"</b> жаңа сабақ <b>"${lesson.name}"</b> қосылды`,
            [LocaleEnum.uz]: `Kursga <b>"${course.name}"</b> yangi dars <b>"${lesson.name}"</b> qo'shildi`,
            [LocaleEnum.de]: `Eine neue Lektion <b>"${lesson.name}"</b> wurde zum Kurs <b>"${course.name}"</b> hinzugefügt`,
            [LocaleEnum.fr]: `Une nouvelle leçon <b>"${lesson.name}"</b> a été ajoutée au cours <b>"${course.name}"</b>`,
            [LocaleEnum.it]: `Una nuova lezione <b>"${lesson.name}"</b> è stata aggiunta al corso <b>"${course.name}"</b>`,
        }[locale];
    }

    public getUrl(): string {
        return this.getLessonUrl();
    }
}
