import { LocaleEnum } from '@prisma/client';
import { z } from 'zod';

import { NotificationMeta, UserEmailParam } from '../abstract/notification-meta.abstract';
import { CourseSchema, MetaAbstractSchema, SchoolSchema } from '../meta.schema';

const CourseAccessBlockSchema = MetaAbstractSchema.omit({ url: true }).extend({
    school: SchoolSchema,
    course: CourseSchema,
});
type CourseAccessBlockMetaType = z.infer<typeof CourseAccessBlockSchema>;

export class CourseAccessBlockMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, CourseAccessBlockSchema);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { course } = this.meta as CourseAccessBlockMetaType;
        return {
            [LocaleEnum.ru]: `Доступ к курсу <b>"${course.name}"</b> заблокирован администратором`,
            [LocaleEnum.en]: `Access to the course <b>"${course.name}"</b> has been blocked by the administrator`,
            [LocaleEnum.es]: `El acceso al curso <b>"${course.name}"</b> ha sido bloqueado por el administrador`,
            [LocaleEnum.kk]: `Курсқа <b>"${course.name}"</b> администратор тарфында блокқалды`,
            [LocaleEnum.uz]: `Kursga <b>"${course.name}"</b> administrator tomonidan kirish bloklangan`,
            [LocaleEnum.de]: `Der Zugriff auf den Kurs <b>"${course.name}"</b> wurde vom Administrator blockiert`,
            [LocaleEnum.fr]: `L'accès au cours <b>"${course.name}"</b> a été bloqué par l'administrateur`,
            [LocaleEnum.it]: `L'accesso al corso <b>"${course.name}"</b> è stato bloccato dall'amministratore`,
        }[locale];
    }

    public getEmailTitle(userParams: UserEmailParam): string {
        const { locale } = userParams;
        const { course } = this.meta as CourseAccessBlockMetaType;
        return {
            [LocaleEnum.ru]: `Вам приостановили доступ к курсу "${course.name}"`,
            [LocaleEnum.en]: `Your access to the course "${course.name}" has been suspended`,
            [LocaleEnum.es]: `Se ha suspendido tu acceso al curso "${course.name}"`,
            [LocaleEnum.kk]: `Сіздің "${course.name}" курсына кіру мүмкіндігіңіз суспендирленді`,
            [LocaleEnum.uz]: `Sizning "${course.name}" kursiga kirish huquqingiz muvaffaqiyatsiz yakunlandi`,
            [LocaleEnum.de]: `Ihr Zugriff auf den Kurs "${course.name}" wurde gesperrt`,
            [LocaleEnum.fr]: `Votre accès au cours "${course.name}" a été suspendu`,
            [LocaleEnum.it]: `Il tuo accesso al corso "${course.name}" è stato sospeso`,
        }[locale];
    }

    public getEmailText(userParams: UserEmailParam): string {
        const { locale } = userParams;
        const { course, school } = this.meta as CourseAccessBlockMetaType;
        const schoolName = school.name;
        return {
            [LocaleEnum.ru]:
                `Администрация школы "${schoolName}" приостановила вам доступ к курсу "${course.name}".` +
                `<br>Весь ваш прогресс сохранён, но доступа к курсу у вас больше нет.`,
            [LocaleEnum.en]:
                `The administration of the school "${schoolName}" has suspended your access to the course "${course.name}".` +
                `<br>All your progress has been saved, but you no longer have access to the course.`,
            [LocaleEnum.es]:
                `La administración de la escuela "${schoolName}" ha suspendido tu acceso al curso "${course.name}".` +
                `<br>Todo tu progreso ha sido guardado, pero ya no tienes acceso al curso.`,
            [LocaleEnum.kk]:
                `"${schoolName}" мектептің басқармасы сіздің "${course.name}" курсына кіру мүмкіндігіңізді суспендирлеуді қарарлайды.` +
                `<br>Сіздің барлық өтулеріңіз сақталған, бірақ енді сізге курсқа кіру мүмкіндігі жоқ.`,
            [LocaleEnum.uz]:
                `"${schoolName}" maktabning boshqarmasi sizning "${course.name}" kursiga kirish huquqingizni to'xtatdi.` +
                `<br>Sizning barcha farqiylaringiz saqlangan, lekin endi sizda kursga kirish huquqi yo'q.`,
            [LocaleEnum.de]:
                `Die Verwaltung der Schule "${schoolName}" hat deinen Zugriff auf den Kurs "${course.name}" gesperrt.` +
                `<br>Dein Fortschritt wurde gespeichert, aber du hast keinen Zugriff mehr auf den Kurs.`,
            [LocaleEnum.fr]:
                `L'administration de l'école "${schoolName}" a suspendu votre accès au cours "${course.name}".` +
                `<br>Tous vos progrès ont été sauvegardés, mais vous n'avez plus accès au cours.`,
            [LocaleEnum.it]:
                `La direzione della scuola "${schoolName}" ha sospeso il tuo accesso al corso "${course.name}".` +
                `<br>Tutti i tuoi progressi sono stati salvati, ma non hai più accesso al corso.`,
        }[locale];
    }
}
