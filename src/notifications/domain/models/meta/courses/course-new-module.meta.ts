import { LocaleEnum } from '@prisma/client';
import { z } from 'zod';

import { NotificationMeta } from '../abstract/notification-meta.abstract';
import { CourseSchema, MetaAbstractSchema, StepSchema } from '../meta.schema';

const CourseNewStepSchema = MetaAbstractSchema.omit({ url: true }).extend({
    course: CourseSchema,
    step: StepSchema,
});
type CourseNewStepMeta = z.infer<typeof CourseNewStepSchema>;

export class CourseNewModuleMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, CourseNewStepSchema);
    }
    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { course, step } = this.meta as CourseNewStepMeta;
        return {
            [LocaleEnum.ru]: `Открыт доступ к модулю <b>"${step.name}"</b> в курсе <b>"${course.name}"</b>`,
            [LocaleEnum.en]: `The module <b>"${step.name}"</b> in the course <b>"${course.name}" is now available</b>`,
            [LocaleEnum.es]: `Se ha concedido acceso al módulo <b>"${step.name}"</b> en el curso <b>"${course.name}"</b>`,
            [LocaleEnum.kk]: `Курс <b>"${course.name}"</b> ішіндегі бөлімге <b>"${step.name}"</b> кіру мүмкіндігі ашық`,
            [LocaleEnum.uz]: `Kurs <b>"${course.name}"</b> ichidagi modulga <b>"${step.name}"</b> kirish ochilgan`,
            [LocaleEnum.de]: `Zugriff auf das Modul <b>"${step.name}"</b> im Kurs <b>"${course.name}"</b> freigeschaltet`,
            [LocaleEnum.fr]: `Accès ouvert au module <b>"${step.name}"</b> dans le cours <b>"${course.name}"</b>`,
            [LocaleEnum.it]: `È stato concesso l'accesso al modulo <b>"${step.name}"</b> nel corso <b>"${course.name}"</b>`,
        }[locale];
    }

    public getUrl(): string {
        return this.getCourseUrl();
    }
}
