import { LocaleEnum } from '@prisma/client';

import { CourseAccessMeta, CourseAccessMetaType } from '../abstract/course-access-meta.abstract';

export class CourseAccessExpiry3DaysMeta extends CourseAccessMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { course } = this.meta as CourseAccessMetaType;
        return {
            [LocaleEnum.ru]: `Доступ к курсу <b>"${course.name}"</b> закончится <b>через 3 дня</b>`,
            [LocaleEnum.en]: `Access to the course <b>"${course.name}"</b> will expire in 3 days`,
            [LocaleEnum.es]: `El acceso al curso <b>"${course.name}"</b> expirará en 3 días`,
            [LocaleEnum.kk]: `Курсқа <b>"${course.name}"</b> кіру мүмкіндігі <b>3 күн ішінде</b> аяқталады`,
            [LocaleEnum.uz]: `Kursga <b>"${course.name}"</b> kirish <b>3 kundan keyin</b> tugaydi`,
            [LocaleEnum.de]: `Der Zugriff auf den Kurs <b>"${course.name}"</b> läuft in 3 Tagen ab`,
            [LocaleEnum.fr]: `L'accès au cours <b>"${course.name}"</b> expire dans 3 jours`,
            [LocaleEnum.it]: `L'accesso al corso <b>"${course.name}"</b> scadrà tra 3 giorni`,
        }[locale];
    }

    public getUrl(): string {
        return this.getCourseUrl();
    }
}
