import { LocaleEnum } from '@prisma/client';
import { z } from 'zod';

import { NotificationMeta } from '../abstract/notification-meta.abstract';
import { CourseSchema, MetaAbstractSchema } from '../meta.schema';

const CourseAccessEndSchema = MetaAbstractSchema.omit({ url: true }).extend({
    course: CourseSchema,
});
type CourseAccessEndMetaType = z.infer<typeof CourseAccessEndSchema>;

export class CourseAccessEndMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, CourseAccessEndSchema);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { course } = this.meta as CourseAccessEndMetaType;
        return {
            [LocaleEnum.ru]: `Истекло время доступа к курсу <b>"${course.name}"</b>`,
            [LocaleEnum.en]: `Access to the course <b>"${course.name}"</b> has expired`,
            [LocaleEnum.es]: `Se ha agotado el tiempo de acceso al curso <b>"${course.name}"</b>`,
            [LocaleEnum.kk]: `Курсқа <b>"${course.name}"</b> кіру мезгілі аяқталды`,
            [LocaleEnum.uz]: `Kursga <b>"${course.name}"</b> kirish muddati tugadi`,
            [LocaleEnum.de]: `Der Zugriff auf den Kurs <b>"${course.name}"</b> ist abgelaufen`,
            [LocaleEnum.fr]: `L'accès au cours <b>"${course.name}"</b> a expiré`,
            [LocaleEnum.it]: `L'accesso al corso <b>"${course.name}"</b> è scaduto`,
        }[locale];
    }
}
