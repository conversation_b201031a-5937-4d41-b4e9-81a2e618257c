import { LocaleEnum } from '@prisma/client';
import { z } from 'zod';

import { NotificationMeta } from '../abstract/notification-meta.abstract';
import { CourseSchema, MetaAbstractSchema, StudentSchema } from '../meta.schema';

const StudentCourseFinishedSchema = MetaAbstractSchema.extend({
    course: CourseSchema,
    student: StudentSchema,
});
type StudentCourseFinishedMetaType = z.infer<typeof StudentCourseFinishedSchema>;

export class StudentCourseFinishedMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, StudentCourseFinishedSchema);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { student, course } = this.meta as StudentCourseFinishedMetaType;
        const studentName = student.name ? `<b>"${student.name}"</b>` : '';
        return {
            [LocaleEnum.ru]: `Ученик ${studentName} завершил курс <b>"${course.name}"</b>`,
            [LocaleEnum.en]: `Student ${studentName} has finished the course <b>"${course.name}"</b>`,
            [LocaleEnum.es]: `El estudiante ${studentName} ha completado el curso <b>"${course.name}"</b>`,
            [LocaleEnum.kk]: `Оқушы ${studentName} курсті бітірді <b>"${course.name}"</b>`,
            [LocaleEnum.uz]: `Talaba ${studentName} kursni yakunladi <b>"${course.name}"</b>`,
            [LocaleEnum.de]: `Der Schüler ${studentName} hat den Kurs abgeschlossen <b>"${course.name}"</b>`,
            [LocaleEnum.fr]: `L'étudiant ${studentName} a terminé le cours <b>"${course.name}"</b>`,
            [LocaleEnum.it]: `Lo studente ${studentName} ha completato il corso <b>"${course.name}"</b>`,
        }[locale];
    }

    public getUrl(): string {
        const { domain, course } = this.meta as StudentCourseFinishedMetaType;
        return `${domain}/students/courses/${course.id}`;
    }
}
