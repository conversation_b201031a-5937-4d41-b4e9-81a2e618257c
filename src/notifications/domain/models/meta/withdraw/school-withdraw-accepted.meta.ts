import { LocaleEnum } from '@prisma/client';

import { SchoolWithdrawMeta } from '../abstract/school-withdraw-meta.abstract';

export class SchoolWithdrawAcceptedMeta extends SchoolWithdrawMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        return {
            [LocaleEnum.ru]: 'Запрос на вывод средств подтвержден',
            [LocaleEnum.en]: 'Withdrawal request has been confirmed',
            [LocaleEnum.es]: 'La solicitud de retiro ha sido confirmada',
            [LocaleEnum.kk]: 'Шығару сұрағы растауленді',
            [LocaleEnum.uz]: "Chiqim so'rovi tasdiqlandi",
            [LocaleEnum.de]: 'Der Auszahlungsantrag wurde bestätigt',
            [LocaleEnum.fr]: 'La demande de retrait a été confirmée',
            [LocaleEnum.it]: 'La richiesta di prelievo è stata confermata',
        }[locale];
    }

    public getUrl(): string {
        return this.getBalanceUrl();
    }
}
