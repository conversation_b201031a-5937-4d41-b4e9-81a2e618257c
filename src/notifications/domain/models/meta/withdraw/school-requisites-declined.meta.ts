import { LocaleEnum } from '@prisma/client';

import { SchoolRequisitesMeta } from '../abstract/school-requisites-meta.abstract';

export class SchoolRequisitesDeclinedMeta extends SchoolRequisitesMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        return {
            [LocaleEnum.ru]: 'Верификация школы отклонена',
            [LocaleEnum.en]: 'School verification has been declined',
            [LocaleEnum.es]: 'La verificación de la escuela ha sido rechazada',
            [LocaleEnum.kk]: 'Мектеп тексерілімі қабылданбаған',
            [LocaleEnum.uz]: 'Maktab tekshiruvi rad etildi',
            [LocaleEnum.de]: 'Die Schulentifikation wurde abgelehnt',
            [LocaleEnum.fr]: "La vérification de l'école a été refusée",
            [LocaleEnum.it]: 'La verifica della scuola è stata rifiutata',
        }[locale];
    }

    public getUrl(): string {
        return this.getBalanceUrl();
    }
}
