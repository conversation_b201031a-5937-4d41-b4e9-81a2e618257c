import { LocaleEnum } from '@prisma/client';

import { SchoolWithdrawMeta } from '../abstract/school-withdraw-meta.abstract';

export class SchoolWithdrawDeclinedMeta extends SchoolWithdrawMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        return {
            [LocaleEnum.ru]: 'Запрос на вывод средств отклонен',
            [LocaleEnum.en]: 'Withdrawal request has been declined',
            [LocaleEnum.es]: 'La solicitud de retiro ha sido rechazada',
            [LocaleEnum.kk]: 'Шығару сұрағы қабылданбаған',
            [LocaleEnum.uz]: "Chiqim so'rovi rad etildi",
            [LocaleEnum.de]: 'Der Auszahlungsantrag wurde abgelehnt',
            [LocaleEnum.fr]: 'La demande de retrait a été refusée',
            [LocaleEnum.it]: 'La richiesta di prelievo è stata rifiutata',
        }[locale];
    }
}
