import { LocaleEnum } from '@prisma/client';

import { SchoolRequisitesMeta } from '../abstract/school-requisites-meta.abstract';

export class SchoolRequisitesAcceptedMeta extends SchoolRequisitesMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        return {
            [LocaleEnum.ru]: 'Верификация школы пройдена',
            [LocaleEnum.en]: 'School verification has passed',
            [LocaleEnum.es]: 'La verificación de la escuela ha sido exitosa',
            [LocaleEnum.kk]: 'Мектеп тексерілімі жеткілікті',
            [LocaleEnum.uz]: 'Maktab tekshiruvi muvaffaqiyatli yakunlandi',
            [LocaleEnum.de]: 'Die Schule wurde erfolgreich verifiziert',
            [LocaleEnum.fr]: "La vérification de l'école a été réussie",
            [LocaleEnum.it]: 'La verifica della scuola è stata superata',
        }[locale];
    }

    public getUrl(): string {
        return this.getBalanceUrl();
    }
}
