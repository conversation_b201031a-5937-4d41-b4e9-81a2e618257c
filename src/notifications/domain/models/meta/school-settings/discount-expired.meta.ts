import { LocaleEnum } from '@prisma/client';

import { NotificationMeta } from '../abstract/notification-meta.abstract';
import { MetaAbstractSchema, PromoCodeSchema } from '../meta.schema';

const DiscountExpiredMetaSchema = MetaAbstractSchema.extend({
    promoCode: PromoCodeSchema,
});

export class DiscountExpiredMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, DiscountExpiredMetaSchema);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        return {
            [LocaleEnum.ru]: `Истек срок действия промокода`,
            [LocaleEnum.en]: `The validity period of the promotion code has expired`,
            [LocaleEnum.es]: `El período de validez del código promocional ha expirado`,
            [LocaleEnum.kk]: `Промокод мезгілі аяқталды`,
            [LocaleEnum.uz]: `Promo-kod muddati tugadi`,
            [LocaleEnum.de]: `Die Gültigkeitsdauer des Promo-Codes ist abgelaufen`,
            [LocaleEnum.fr]: `La période de validité du code promotionnel a expiré`,
            [LocaleEnum.it]: `Il periodo di validità del codice promozionale è scaduto`,
        }[locale];
    }

    public getUrl(): string {
        const { domain } = this.meta as { domain: string };
        return `${domain}/school/settings/payment-gateways`;
    }
}
