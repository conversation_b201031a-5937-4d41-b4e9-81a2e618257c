import { LocaleEnum } from '@prisma/client';
import { z } from 'zod';

import { NotificationMeta } from '../abstract/notification-meta.abstract';
import { FeatureCodeEnum, FeatureSchema, MetaAbstractSchema } from '../meta.schema';

const FeatureLimitExceededSchema = MetaAbstractSchema.extend({
    feature: FeatureSchema,
});

type FeatureLimitExceededMetaType = z.infer<typeof FeatureLimitExceededSchema>;

export class FeatureLimitExceededMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, FeatureLimitExceededSchema);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { feature } = this.meta as FeatureLimitExceededMetaType;
        const usagePercentage = ((feature.usage_limit / feature.limit) * 100).toFixed(0);
        const code = feature.code as FeatureCodeEnum;

        return {
            [LocaleEnum.ru]: {
                [FeatureCodeEnum.TOTAL_USER_LIMIT]: `База обучающихся заполнена на <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.STUDENT_LIMIT]: `Кол-во учеников в этом месяце заполнено на <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.STORAGE_LIMIT]: `Хранилище заполнено на <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.EMPLOYEE_LIMIT]: `Кол-во сотрудников заполнено на <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.COURSE_LIMIT]: `Кол-во курсов заполнено на <b>${usagePercentage}%</b>`,
            },

            [LocaleEnum.en]: {
                [FeatureCodeEnum.TOTAL_USER_LIMIT]: `The base of students is filled with <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.STUDENT_LIMIT]: `The number of students in this month is filled with <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.STORAGE_LIMIT]: `The storage is filled with <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.EMPLOYEE_LIMIT]: `The number of employees is filled with <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.COURSE_LIMIT]: `The number of courses is filled with <b>${usagePercentage}%</b>`,
            },

            [LocaleEnum.es]: {
                [FeatureCodeEnum.TOTAL_USER_LIMIT]: `La base de estudiantes está llenada con <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.STUDENT_LIMIT]: `El número de estudiantes este mes está llenado con <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.STORAGE_LIMIT]: `El almacenamiento está llenado con <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.EMPLOYEE_LIMIT]: `El número de empleados está llenado con <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.COURSE_LIMIT]: `El número de cursos está llenado con <b>${usagePercentage}%</b>`,
            },

            [LocaleEnum.kk]: {
                [FeatureCodeEnum.TOTAL_USER_LIMIT]: `Оқушылар базасы толықтырылды <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.STUDENT_LIMIT]: `Бұл айдағы оқушылар саны толықтырылды <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.STORAGE_LIMIT]: `Сақтау жері толықтырылды <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.EMPLOYEE_LIMIT]: `Исемдіктер саны толықтырылды <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.COURSE_LIMIT]: `Курстар саны толықтырылды <b>${usagePercentage}%</b>`,
            },

            [LocaleEnum.uz]: {
                [FeatureCodeEnum.TOTAL_USER_LIMIT]: `Talabalar bazasi <b>${usagePercentage}%</b> to'lgan`,
                [FeatureCodeEnum.STUDENT_LIMIT]: `Ushbu oyda talabalar soni <b>${usagePercentage}%</b> to'lgan`,
                [FeatureCodeEnum.STORAGE_LIMIT]: `Saqlash joyi <b>${usagePercentage}%</b> to'lgan`,
                [FeatureCodeEnum.EMPLOYEE_LIMIT]: `Xodimlar soni <b>${usagePercentage}%</b> to'lgan`,
                [FeatureCodeEnum.COURSE_LIMIT]: `Kurslar soni <b>${usagePercentage}%</b> to'lgan`,
            },

            [LocaleEnum.de]: {
                [FeatureCodeEnum.TOTAL_USER_LIMIT]: `Die Schülerdatenbank ist mit <b>${usagePercentage}%</b> gefüllt`,
                [FeatureCodeEnum.STUDENT_LIMIT]: `Die Anzahl der Schüler in diesem Monat ist um <b>${usagePercentage}%</b> gefüllt`,
                [FeatureCodeEnum.STORAGE_LIMIT]: `Der Speicherplatz ist um <b>${usagePercentage}%</b> gefüllt`,
                [FeatureCodeEnum.EMPLOYEE_LIMIT]: `Die Anzahl der Mitarbeiter ist um <b>${usagePercentage}%</b> gefüllt`,
                [FeatureCodeEnum.COURSE_LIMIT]: `Die Anzahl der Kurse ist um <b>${usagePercentage}%</b> gefüllt`,
            },

            [LocaleEnum.fr]: {
                [FeatureCodeEnum.TOTAL_USER_LIMIT]: `La base d'étudiants est remplie avec <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.STUDENT_LIMIT]: `Le nombre d'étudiants ce mois-ci est rempli à <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.STORAGE_LIMIT]: `Le stockage est rempli à <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.EMPLOYEE_LIMIT]: `Le nombre d'employés est rempli à <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.COURSE_LIMIT]: `Le nombre de cours est rempli à <b>${usagePercentage}%</b>`,
            },

            [LocaleEnum.it]: {
                [FeatureCodeEnum.TOTAL_USER_LIMIT]: `Il database degli studenti è riempito con <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.STUDENT_LIMIT]: `Il numero di studenti di questo mese è riempito al <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.STORAGE_LIMIT]: `Lo spazio di archiviazione è riempito al <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.EMPLOYEE_LIMIT]: `Il numero di dipendenti è riempito al <b>${usagePercentage}%</b>`,
                [FeatureCodeEnum.COURSE_LIMIT]: `Il numero di corsi è riempito al <b>${usagePercentage}%</b>`,
            },
        }[locale][code];
    }

    public getUrl(): string {
        return this.getTariffUrl();
    }
}
