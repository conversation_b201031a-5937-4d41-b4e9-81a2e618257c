import { LocaleEnum } from '@prisma/client';

import { UserEmailParam } from '../abstract/notification-meta.abstract';
import { WebinarMeta, WebinarMetaType } from '../abstract/webinar-meta.abstract';

export class WebinarStartStudentMeta extends WebinarMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const meta = this.meta as WebinarMetaType;
        return {
            [LocaleEnum.ru]: `Вебинар <b>"${meta.lesson.name}"</b> начался`,
            [LocaleEnum.en]: `The webinar <b>"${meta.lesson.name}"</b> has started`,
            [LocaleEnum.es]: `El webinar <b>"${meta.lesson.name}"</b> ha comenzado`,
            [LocaleEnum.kk]: `Вебинар <b>"${meta.lesson.name}"</b> басталды`,
            [LocaleEnum.uz]: `Videokonferensiya <b>"${meta.lesson.name}"</b> boshlandi`,
            [LocaleEnum.de]: `Der Webinar <b>"${meta.lesson.name}"</b> hat begonnen`,
            [LocaleEnum.fr]: `Le webinar <b>"${meta.lesson.name}"</b> a commencé`,
            [LocaleEnum.it]: `Il webinar <b>"${meta.lesson.name}"</b> è iniziato`,
        }[locale];
    }

    public getEmailTitle(userParams: UserEmailParam): string {
        const { lesson } = this.meta as WebinarMetaType;
        const { locale } = userParams;
        return {
            [LocaleEnum.ru]: `Вебинар "${lesson.name}" уже начинается!`,
            [LocaleEnum.en]: `The webinar "${lesson.name}" is starting soon!`,
            [LocaleEnum.es]: `¡El webinar "${lesson.name}" ya está comenzando!`,
            [LocaleEnum.kk]: `Вебинар "${lesson.name}" басталуына дейін жақсы уақыт!`,
            [LocaleEnum.uz]: `Vebinar "${lesson.name}" yaqinlashmoqda!`,
            [LocaleEnum.de]: `Der Webinar "${lesson.name}" beginnt gleich!`,
            [LocaleEnum.fr]: `Le webinar "${lesson.name}" commence bientôt!`,
            [LocaleEnum.it]: `Il webinar "${lesson.name}" sta per iniziare!`,
        }[locale];
    }

    public getEmailText(): string {
        return null;
    }

    public getEmailButtonText(locale: LocaleEnum): string {
        return {
            [LocaleEnum.ru]: 'Перейти к вебинару',
            [LocaleEnum.en]: 'Go to webinar',
            [LocaleEnum.es]: 'Ir al webinar',
            [LocaleEnum.kk]: 'Вебинарға өту',
            [LocaleEnum.uz]: 'Vebinariga o‘tish',
            [LocaleEnum.de]: 'Zum Webinar gehen',
            [LocaleEnum.fr]: 'Aller au webinar',
            [LocaleEnum.it]: 'Vai al webinar',
        }[locale];
    }

    public getUrl(): string {
        return this.getLessonUrl();
    }
}
