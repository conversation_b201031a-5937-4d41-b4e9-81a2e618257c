import { LocaleEnum } from '@prisma/client';

import { UserEmailParam } from '../abstract/notification-meta.abstract';
import { WebinarMeta, WebinarMetaType } from '../abstract/webinar-meta.abstract';

export class StudentUpcomingWebinarMeta extends WebinarMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { lesson, webinar } = this.meta as WebinarMetaType;
        const distance = this.formatDistance(new Date(webinar.startAt), locale);
        return {
            [LocaleEnum.ru]: `До начала вебинара <b>"${lesson.name}"</b> ${distance}`,
            [LocaleEnum.en]: `The webinar <b>"${lesson.name}"</b> starts in ${distance}`,
            [LocaleEnum.es]: `Quedan ${distance} para el inicio del webinar <b>"${lesson.name}"</b>`,
            [LocaleEnum.kk]: `Вебинар <b>"${lesson.name}"</b> басталуына дейін ${distance} қалды`,
            [LocaleEnum.uz]: `Videokonferensiya <b>"${lesson.name}"</b> boshlanishiga ${distance} qoldi`,
            [LocaleEnum.de]: `Der Webinar <b>"${lesson.name}"</b> beginnt in ${distance}`,
            [LocaleEnum.fr]: `Il reste ${distance} avant le début du webinar <b>"${lesson.name}"</b>`,
            [LocaleEnum.it]: `Mancano ${distance} all'inizio del webinar <b>"${lesson.name}"</b>`,
        }[locale];
    }
    public getEmailTitle(userParams: UserEmailParam): string {
        const { lesson, webinar } = this.meta as WebinarMetaType;
        const { locale } = userParams;
        const timezoneAbbr = userParams.timezoneAbbr || this.defaultTimeZoneAbbr;
        const timezone = userParams.timezone || this.defaultTimeZone;
        const fDate = this.formatDateWithTimeZone(webinar.startAt, timezone);
        const fTime = this.formatTimeWithTimeZone(webinar.startAt, timezone);

        return {
            [LocaleEnum.ru]: `Вебинар "${lesson.name}" начнётся ${fDate} в ${fTime} (${timezoneAbbr})`,
            [LocaleEnum.en]: `Webinar "${lesson.name}" starts on ${fDate} at ${fTime} (${timezoneAbbr})`,
            [LocaleEnum.es]: `El webinar "${lesson.name}" comienza el ${fDate} a las ${fTime} (${timezoneAbbr})`,
            [LocaleEnum.kk]: `Вебинар "${lesson.name}" басталады ${fDate} сағат ${fTime} (${timezoneAbbr})`,
            [LocaleEnum.uz]: `Videokonferensiya "${lesson.name}" boshlanadi ${fDate} soat ${fTime} (${timezoneAbbr})`,
            [LocaleEnum.de]: `Der Webinar "${lesson.name}" beginnt am ${fDate} um ${fTime} Uhr (${timezoneAbbr})`,
            [LocaleEnum.fr]: `Le webinar "${lesson.name}" commence le ${fDate} à ${fTime} (${timezoneAbbr})`,
            [LocaleEnum.it]: `Il webinar "${lesson.name}" inizia il ${fDate} alle ${fTime} (${timezoneAbbr})`,
        }[locale];
    }

    public getEmailText(userParams: UserEmailParam): string {
        const { locale } = userParams;
        return {
            [LocaleEnum.ru]: `Лучше подключиться заранее, чтобы не пропустить начало вебинара.`,
            [LocaleEnum.en]: `It's better to connect in advance not to miss the start of the webinar.`,
            [LocaleEnum.es]: `Es mejor conectarse con antelación para no perderse el inicio del webinar.`,
            [LocaleEnum.kk]: `Вебинардың басталуын қайта алмау үшін бастапқыда қосылудың жақсы.`,
            [LocaleEnum.uz]: `Vebinars boshlanishini qayta olmash uchun oldindan ulanish yaxshi.`,
            [LocaleEnum.de]: `Es ist besser, sich im Voraus zu verbinden, um den Beginn des Webinars nicht zu verpassen.`,
            [LocaleEnum.fr]: `Il vaut mieux se connecter à l'avance pour ne pas rater le début du webinar.`,
            [LocaleEnum.it]: `È meglio connettersi in anticipo per non perdere l'inizio del webinar.`,
        }[locale];
    }

    public getEmailButtonText(locale: LocaleEnum): string {
        return {
            [LocaleEnum.ru]: 'Перейти к вебинару',
            [LocaleEnum.en]: 'Go to webinar',
            [LocaleEnum.es]: 'Ir al webinar',
            [LocaleEnum.kk]: 'Вебинарға өту',
            [LocaleEnum.uz]: 'Vebinariga o‘tish',
            [LocaleEnum.de]: 'Zum Webinar gehen',
            [LocaleEnum.fr]: 'Aller au webinar',
            [LocaleEnum.it]: 'Vai al webinar',
        }[locale];
    }

    public getUrl(): string {
        return this.getLessonUrl();
    }
}

//    Вебинар &laquo;{{ lessonName }}&raquo; начнётся {{ webinarStartAt | date('d.m.Y') }} в {{ webinarStartAt | date('H:i', userTimeZone) }} ({{ timezoneAbbr }})
