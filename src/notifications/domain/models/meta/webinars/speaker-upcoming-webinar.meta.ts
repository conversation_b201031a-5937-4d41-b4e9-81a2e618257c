import { LocaleEnum } from '@prisma/client';

import { UserEmailParam } from '../abstract/notification-meta.abstract';
import { WebinarMeta, WebinarMetaType } from '../abstract/webinar-meta.abstract';

export class SpeakerUpcomingWebinarMeta extends WebinarMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { lesson, webinar } = this.meta as WebinarMetaType;
        const distance = this.formatDistance(new Date(webinar.startAt), locale);
        return {
            [LocaleEnum.ru]: `До начала вебинара <b>"${lesson.name}"</b> осталось ${distance}`,
            [LocaleEnum.en]: `The webinar <b>"${lesson.name}"</b> starts in ${distance}`,
            [LocaleEnum.es]: `Quedan ${distance} para el inicio del webinar <b>"${lesson.name}"</b>`,
            [LocaleEnum.kk]: `Вебинар <b>"${lesson.name}"</b> басталуына дейін ${distance} қалды`,
            [LocaleEnum.uz]: `Videokonferensiya <b>"${lesson.name}"</b> boshlanishiingga ${distance} qoldi`,
            [LocaleEnum.de]: `Der Webinar <b>"${lesson.name}"</b> beginnt in ${distance}`,
            [LocaleEnum.fr]: `Il reste ${distance} avant le début du webinar <b>"${lesson.name}"</b>`,
            [LocaleEnum.it]: `Mancano ${distance} all'inizio del webinar <b>"${lesson.name}"</b>`,
        }[locale];
    }

    public getEmailTitle(userParams: UserEmailParam): string {
        const { lesson, webinar } = this.meta as WebinarMetaType;
        const { locale } = userParams;
        const timezoneAbbr = userParams.timezoneAbbr || this.defaultTimeZoneAbbr;
        const timezone = userParams.timezone || this.defaultTimeZone;
        const fDate = this.formatDateWithTimeZone(webinar.startAt, timezone);
        const fTime = this.formatTimeWithTimeZone(webinar.startAt, timezone);

        return {
            [LocaleEnum.ru]: `Вебинар "${lesson.name}" начнётся ${fDate} в ${fTime} (${timezoneAbbr})`,
            [LocaleEnum.en]: `Webinar "${lesson.name}" starts on ${fDate} at ${fTime} (${timezoneAbbr})`,
            [LocaleEnum.es]: `El webinar "${lesson.name}" comienza el ${fDate} a las ${fTime} (${timezoneAbbr})`,
            [LocaleEnum.kk]: `Вебинар "${lesson.name}" басталады ${fDate} сағат ${fTime} (${timezoneAbbr})`,
            [LocaleEnum.uz]: `Videokonferensiya "${lesson.name}" boshlanadi ${fDate} soat ${fTime} (${timezoneAbbr})`,
            [LocaleEnum.de]: `Der Webinar "${lesson.name}" beginnt am ${fDate} um ${fTime} Uhr (${timezoneAbbr})`,
            [LocaleEnum.fr]: `Le webinar "${lesson.name}" commence le ${fDate} à ${fTime} (${timezoneAbbr})`,
            [LocaleEnum.it]: `Il webinar "${lesson.name}" inizia il ${fDate} alle ${fTime} (${timezoneAbbr})`,
        }[locale];
    }

    public getEmailText(userParams: UserEmailParam): string {
        const { locale } = userParams;
        return {
            [LocaleEnum.ru]: `Не забудьте подключиться заранее, чтобы успеть проверить камеру и микрофон и предварительно всё настроить.`,
            [LocaleEnum.en]: `Don't forget to connect in advance to have time to check your camera and microphone and set everything up beforehand.`,
            [LocaleEnum.es]: `No olvides conectarte con antelación para tener tiempo de revisar tu cámara y micrófono y configurarlo todo previamente.`,
            [LocaleEnum.kk]: `Камераныз және микроскоптыңызды тексеріп, барлығын алдын ала жобалау үшін кез келетінше қосылыңыз.`,
            [LocaleEnum.uz]: `Kamerangiz va mikrofoningizni tekshirish va barcha sozlamalarni oldindan bajaring uchun vebinarga oldindan ulaning.`,
            [LocaleEnum.de]: `Vergessen Sie nicht, sich im Voraus zu verbinden, um genügend Zeit zu haben, Ihre Kamera und Ihr Mikrofon zu überprüfen und alles vorab einzurichten.`,
            [LocaleEnum.fr]: `N'oubliez pas de vous connecter à l'avance pour avoir le temps de vérifier votre caméra et votre microphone et tout configurer préalablement.`,
            [LocaleEnum.it]: `Non dimenticare di connetterti in anticipo per avere il tempo di controllare la tua videocamera e il microfono e configurare tutto preventivamente.`,
        }[locale];
    }

    public getEmailButtonText(locale: LocaleEnum): string {
        return {
            [LocaleEnum.ru]: 'Перейти к вебинару',
            [LocaleEnum.en]: 'Go to webinar',
            [LocaleEnum.es]: 'Ir al webinar',
            [LocaleEnum.kk]: 'Вебинарға өту',
            [LocaleEnum.uz]: 'Vebinariga o‘tish',
            [LocaleEnum.de]: 'Zum Webinar gehen',
            [LocaleEnum.fr]: 'Aller au webinar',
            [LocaleEnum.it]: 'Vai al webinar',
        }[locale];
    }

    public getUrl(): string {
        const { lesson, course, domain } = this.meta as WebinarMetaType;
        return `${domain}/course/${course.id}/constructor/${lesson.uuid}`;
    }
}
