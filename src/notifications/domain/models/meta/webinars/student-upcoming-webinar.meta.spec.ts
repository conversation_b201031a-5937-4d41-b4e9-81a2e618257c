import { ActionEnum, LocaleEnum } from '@prisma/client';

import { META } from '../../../../../../tests/integration/notify/notification-meta-constants';
import { UserEmailParam } from '../abstract/notification-meta.abstract';
import { WebinarMetaType } from '../abstract/webinar-meta.abstract';
import { StudentUpcomingWebinarMeta } from './student-upcoming-webinar.meta';

const webinarMeta = META[ActionEnum.ACTION_COURSE_UPCOMING_WEBINAR_STUDENT].meta;
const lessonName = webinarMeta.lesson.name;

const moscow: UserEmailParam = {
    timezone: 'Europe/Moscow',
    timezoneAbbr: 'MSK',
    locale: LocaleEnum.ru,
};
const newYork: UserEmailParam = {
    timezone: 'America/New_York',
    timezoneAbbr: 'EDT',
    locale: LocaleEnum.en,
};
const berlin: UserEmailParam = {
    timezone: 'Europe/Berlin',
    timezoneAbbr: 'CET',
    locale: LocaleEnum.de,
};

describe('StudentUpcomingWebinarMeta', () => {
    const createMeta = (startAt: Date, createdAt: Date): StudentUpcomingWebinarMeta => {
        const meta: WebinarMetaType = {
            ...webinarMeta,
            webinar: { startAt: startAt.getTime() },
        };
        return new StudentUpcomingWebinarMeta(meta, createdAt);
    };

    it('должен корректно форматировать время в шаблоне письма', () => {
        const createdAt = new Date('2023-10-01T10:00:00Z');
        const startAt = new Date('2023-10-05T14:30:00Z'); // Время начала вебинара
        const meta = createMeta(startAt, createdAt);

        const moscowResult = meta.getEmailTitle(moscow);
        const berlinResult = meta.getEmailTitle(berlin);
        const newYorkResult = meta.getEmailTitle(newYork);

        expect(moscowResult).toBe('Вебинар "Вводное занятие" начнётся 05.10.2023 в 17:30 (MSK)'); // +3
        expect(berlinResult).toBe('Der Webinar "Вводное занятие" beginnt am 05.10.2023 um 16:30 Uhr (CET)'); // +2
        expect(newYorkResult).toBe('Webinar "Вводное занятие" starts on 05.10.2023 at 10:30 (EDT)'); // -5
    });

    describe('должен корректно форматировать время до начала вебинара в уведомлении', () => {
        it('должен корректно форматировать разницу в 1 день', () => {
            const createdAt = new Date('2023-10-01T10:00:00Z');
            const startAt = new Date('2023-10-02T10:00:00Z');
            const meta = createMeta(startAt, createdAt);

            const resultRu = meta.getMessageText(LocaleEnum.ru);
            const resultEn = meta.getMessageText(LocaleEnum.en);
            expect(resultRu).toBe(`До начала вебинара <b>"${lessonName}"</b> 1 день`);
            expect(resultEn).toBe(`The webinar <b>"${lessonName}"</b> starts in 1 day`);
        });

        it('должен корректно форматировать разницу в 1 час', () => {
            const createdAt = new Date('2023-10-01T10:00:00Z');
            const startAt = new Date('2023-10-01T11:00:00Z'); // ровно через час
            const meta = createMeta(startAt, createdAt);

            const resultRu = meta.getMessageText(LocaleEnum.ru);
            const resultEn = meta.getMessageText(LocaleEnum.en);

            expect(resultRu).toBe(`До начала вебинара <b>"${lessonName}"</b> около 1 часа`);
            expect(resultEn).toBe(`The webinar <b>"${lessonName}"</b> starts in about 1 hour`);
        });

        it('должен корректно форматировать разницу в несколько минут', () => {
            const createdAt = new Date('2023-10-01T10:00:00Z');
            const startAt = new Date('2023-10-01T10:15:00Z'); // через 15 минут
            const meta = createMeta(startAt, createdAt);

            const resultRu = meta.getMessageText(LocaleEnum.ru);
            const resultEn = meta.getMessageText(LocaleEnum.en);

            expect(resultRu).toBe(`До начала вебинара <b>"${lessonName}"</b> 15 минут`);
            expect(resultEn).toBe(`The webinar <b>"${lessonName}"</b> starts in 15 minutes`);
        });

        it('должен корректно форматировать разницу в несколько секунд', () => {
            const createdAt = new Date('2023-10-01T10:00:00Z');
            const startAt = new Date('2023-10-01T10:00:30Z'); // через 30 секунд
            const meta = createMeta(startAt, createdAt);

            const resultRu = meta.getMessageText(LocaleEnum.ru);
            const resultEn = meta.getMessageText(LocaleEnum.en);

            expect(resultRu).toBe(`До начала вебинара <b>"${lessonName}"</b> 1 минута`);
            expect(resultEn).toBe(`The webinar <b>"${lessonName}"</b> starts in 1 minute`);
        });
    });
});
