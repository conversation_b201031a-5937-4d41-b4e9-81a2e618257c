import { LocaleEnum } from '@prisma/client';

import { NotificationMeta, UserEmailParam } from '../abstract/notification-meta.abstract';

export class UpcomingSubscriptionEnd7DaysMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        return {
            [LocaleEnum.ru]: 'Осталось 7 дней до окончания подписки',
            [LocaleEnum.en]: 'Your subscription will expire in 7 days',
            [LocaleEnum.es]: 'Quedan 7 días para la expiración de tu suscripción',
            [LocaleEnum.kk]: 'Жазбалықыңыз аяқталуына 7 күн қалды',
            [LocaleEnum.uz]: 'Obuna tugashiga 7 kundan oldin qoldi',
            [LocaleEnum.de]: 'Dein Abo läuft in 7 Tagen ab',
            [LocaleEnum.fr]: 'Votre abonnement expire dans 7 jours',
            [LocaleEnum.it]: 'Il tuo abbonamento scadrà tra 7 giorni',
        }[locale];
    }

    public getEmailTitle(userParams: UserEmailParam): string {
        const { locale } = userParams;
        return {
            [LocaleEnum.ru]: `Ваш тарифный план закончится меньше чем через 7 дней.`,
            [LocaleEnum.en]: `Your pricing plan will expire in less than 7 days.`,
            [LocaleEnum.es]: `Tu plan de tarifas expirará en menos de 7 días.`,
            [LocaleEnum.kk]: `Сіздің тарифтік планыңыз 7 күннен аз уақыт ішінде аяқталады.`,
            [LocaleEnum.uz]: `Sizning narxli plangiz 7 kundan oldin tugaydi.`,
            [LocaleEnum.de]: `Ihr Preismodell läuft in weniger als 7 Tagen ab.`,
            [LocaleEnum.fr]: `Votre forfait expire dans moins de 7 jours.`,
            [LocaleEnum.it]: `Il tuo piano tariffario scadrà in meno di 7 giorni.`,
        }[locale];
    }

    public getEmailText(userParams: UserEmailParam): string {
        const { locale } = userParams;
        return {
            [LocaleEnum.ru]: `Пожалуйста, не забудьте его продлить!`,
            [LocaleEnum.en]: `Please don't forget to renew it!`,
            [LocaleEnum.es]: `¡Por favor, no olvides renovarlo!`,
            [LocaleEnum.kk]: `Ияңғы тіркелуіңізді ұзартуға уәде етіңіз жоқ!`,
            [LocaleEnum.uz]: `Iltimos, unutmang va uni davom ettiring!`,
            [LocaleEnum.de]: `Bitte vergessen Sie nicht, es zu verlängern!`,
            [LocaleEnum.fr]: `Veuillez ne pas oublier de le renouveler !`,
            [LocaleEnum.it]: `Per favore, non dimenticare di rinnovarlo!`,
        }[locale];
    }

    public getEmailButtonText(locale: LocaleEnum): string {
        return {
            [LocaleEnum.ru]: 'Продлить',
            [LocaleEnum.en]: 'Extend',
            [LocaleEnum.es]: 'Prolongar',
            [LocaleEnum.kk]: 'Ұзарту',
            [LocaleEnum.uz]: 'Davom ettiring',
            [LocaleEnum.de]: 'Verlängern',
            [LocaleEnum.fr]: 'Prolonger',
            [LocaleEnum.it]: 'Prolungare',
        }[locale];
    }

    public getUrl(): string {
        return this.getPlansUrl();
    }
}
