import { LocaleEnum } from '@prisma/client';

import { NotificationMeta, UserEmailParam } from '../abstract/notification-meta.abstract';

export class SubscriptionExpiredMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        return {
            [LocaleEnum.ru]: 'Подписка истекла. Ученикам заблокирован доступ к обучению',
            [LocaleEnum.en]: 'Subscription has expired. Students have been blocked from learning',
            [LocaleEnum.es]: 'La suscripción ha expirado. Los estudiantes han sido bloqueados para el aprendizaje.',
            [LocaleEnum.kk]: 'Жазбалық аяқталды. Оқушылардың оқуға кіруі блокталды.',
            [LocaleEnum.uz]: "Obuna muddati tugadi. Talabalar o'qishdan bloklangan.",
            [LocaleEnum.de]: 'Das Abo ist abgelaufen. Den Schülern wurde der Zugriff auf das Lernen gesperrt.',
            [LocaleEnum.fr]: "L'abonnement a expiré. Les étudiants ont été bloqués pour l'apprentissage.",
            [LocaleEnum.it]: "L'abbonamento è scaduto. Gli studenti sono stati bloccati dall’apprendimento.",
        }[locale];
    }

    public getEmailTitle(userParams: UserEmailParam): string {
        const { locale } = userParams;
        return this.getMessageText(locale);
    }

    public getEmailText(userParams: UserEmailParam): string {
        const { locale } = userParams;
        return {
            [LocaleEnum.ru]: `Ваша подписка на Skillspace истекла. Чтобы вы и ваши ученики могли продолжать использование платформы, необходимо продлить подписку.`,
            [LocaleEnum.en]: `Your Skillspace subscription has expired. To continue using the platform for you and your students, you need to renew your subscription.`,
            [LocaleEnum.es]: `Tu suscripción a Skillspace ha expirado. Para que tú y tus estudiantes puedan continuar usando la plataforma, es necesario renovar la suscripción.`,
            [LocaleEnum.kk]: `Sizdin Skillspace абонементіңіз аяқталды. Сенімде және оқушыларыңыздың платформаны пайдалануын әртүрлікеу үшін абонементті ұзарту керек.`,
            [LocaleEnum.uz]: `Sizning Skillspace obunangiz muddati tugagan. Siz va talabalariz platformani davomida ishlatish uchun obunani uzaytirishingiz kerak.`,
            [LocaleEnum.de]: `Ihr Skillspace-Abonnement ist abgelaufen. Damit Sie und Ihre Schüler weiterhin die Plattform nutzen können, müssen Sie das Abonnement verlängern.`,
            [LocaleEnum.fr]: `Votre abonnement Skillspace a expiré. Pour continuer à utiliser la plateforme pour vous et vos étudiants, il est nécessaire de renouveler votre abonnement.`,
            [LocaleEnum.it]: `Il tuo abbonamento a Skillspace è scaduto. Per continuare a utilizzare la piattaforma per te e i tuoi studenti, è necessario rinnovare l'abbonamento.`,
        }[locale];
    }

    public getEmailButtonText(locale: LocaleEnum): string {
        return {
            [LocaleEnum.ru]: 'Продлить подписку сейчас',
            [LocaleEnum.en]: 'Renew subscription now',
            [LocaleEnum.es]: 'Renovar suscripción ahora',
            [LocaleEnum.kk]: 'Қазір абонементті ұзарту',
            [LocaleEnum.uz]: 'Hozir obunani uzaytiring',
            [LocaleEnum.de]: 'Abonnement jetzt verlängern',
            [LocaleEnum.fr]: 'Renouveler l’abonnement maintenant',
            [LocaleEnum.it]: "Rinnova l'abbonamento ora",
        }[locale];
    }

    public getUrl(): string {
        return this.getPlansUrl();
    }
}
