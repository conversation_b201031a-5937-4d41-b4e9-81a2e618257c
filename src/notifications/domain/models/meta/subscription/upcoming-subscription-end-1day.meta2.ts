import { LocaleEnum } from '@prisma/client';

import { NotificationMeta, UserEmailParam } from '../abstract/notification-meta.abstract';

export class UpcomingSubscriptionEnd1DayMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        return {
            [LocaleEnum.ru]: 'Сегодня подписка на Skillspace закончится и ученики потеряют доступ',
            [LocaleEnum.en]: 'Today, your Skillspace subscription will expire and students will lose access',
            [LocaleEnum.es]: 'Hoy tu suscripción a Skillspace expirará y los estudiantes perderán el acceso.',
            [LocaleEnum.kk]: 'Бүгін Skillspace жазбалығыңыз аяқталады және оқушылар кірістіктерін жоюады.',
            [LocaleEnum.uz]: 'Bugun Skillspace obunasining muddati tugaydi va talabalar kirish huquqini yo`qotadi.',
            [LocaleEnum.de]: 'Heute läuft deine Skillspace-Abonnement ab, und die Schüler verlieren den Zugang.',
            [LocaleEnum.fr]: "Aujourd'hui, votre abonnement Skillspace expire et les étudiants perdront l'accès.",
            [LocaleEnum.it]: "Oggi il tuo abbonamento a Skillspace scadrà e gli studenti perderanno l'accesso.",
        }[locale];
    }

    public getEmailTitle(userParams: UserEmailParam): string {
        const { locale } = userParams;
        return {
            [LocaleEnum.ru]: `Ваш тарифный план закончится меньше чем через 24 часа`,
            [LocaleEnum.en]: `Your pricing plan will expire in less than 24 hours`,
            [LocaleEnum.es]: `Tu plan de tarifas expirará en menos de 24 horas`,
            [LocaleEnum.kk]: `Сіздің тарифтік планыңыз 24 сағаттан аз уақыт ішінде аяқталады`,
            [LocaleEnum.uz]: `Sizning narxli plangiz 24 soatdan oldin tugaydi`,
            [LocaleEnum.de]: `Ihr Preismodell läuft in weniger als 24 Stunden ab`,
            [LocaleEnum.fr]: `Votre forfait expire dans moins de 24 heures`,
            [LocaleEnum.it]: `Il tuo piano tariffario scadrà in meno di 24 ore`,
        }[locale];
    }

    public getEmailText(userParams: UserEmailParam): string {
        const { locale } = userParams;
        return {
            [LocaleEnum.ru]: `Когда план закончится, доступ к обучающему кабинету у учеников будет закрыт, а функционал кабинета администратора будет ограничен страницей "Тарифы".`,
            [LocaleEnum.en]: `When the plan expires, students' access to the learning dashboard will be closed, and the administrator dashboard functionality will be restricted to the "Pricing" page.`,
            [LocaleEnum.es]: `Cuando el plan termine, el acceso de los estudiantes al panel de aprendizaje se cerrará y la funcionalidad del panel de administrador se limitará a la página "Precios".`,
            [LocaleEnum.kk]: `План аяқталғанда, оқушылардың оқу кабинетіне қол жетімі жабылып, администратор кабинетінің функциялары "Тарифтер" бетіне шектеледі.`,
            [LocaleEnum.uz]: `Plan tugaganda talabalar o'quv kabinetiga kirish blokiriladi va administrator kabinetining funktsiyalari "Narxlash" sahifasiga cheklanadi.`,
            [LocaleEnum.de]: `Wenn das Modell abläuft, wird der Schülerzugriff auf das Lern-dashboard gesperrt und die Administrator-Dashboard-Funktionalität auf die "Preise"-Seite beschränkt.`,
            [LocaleEnum.fr]: `Lorsque le forfait expire, l'accès des étudiants au tableau de bord d'apprentissage sera fermé et la fonctionnalité du tableau de bord d'administration sera limitée à la page "Tarifs".`,
            [LocaleEnum.it]: `Quando il piano scade, l'accesso degli studenti al dashboard di apprendimento verrà chiuso e la funzionalità del dashboard dell'amministratore sarà limitata alla pagina "Tariffe".`,
        }[locale];
    }

    public getEmailButtonText(locale: LocaleEnum): string {
        return {
            [LocaleEnum.ru]: 'Продлить подписку',
            [LocaleEnum.en]: 'Renew subscription',
            [LocaleEnum.es]: 'Renovar suscripción',
            [LocaleEnum.kk]: 'Абонементті ұзарту',
            [LocaleEnum.uz]: 'Obunani uzaytiring',
            [LocaleEnum.de]: 'Abonnement verlängern',
            [LocaleEnum.fr]: 'Renouveler l’abonnement',
            [LocaleEnum.it]: "Rinnova l'abbonamento",
        }[locale];
    }

    public getUrl(): string {
        return this.getPlansUrl();
    }
}
