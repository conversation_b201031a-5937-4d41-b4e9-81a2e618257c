import { LocaleEnum } from '@prisma/client';

import { NotificationMeta } from '../abstract/notification-meta.abstract';

export class RecurrentPaymentFailureMeta extends NotificationMeta {
    constructor(meta: { url: string }, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        return {
            [LocaleEnum.ru]: 'Ошибка автоматического продления подписки.',
            [LocaleEnum.en]: 'There was an error with the automatic subscription renewal.',
            [LocaleEnum.es]: 'Ocurrió un error al renovar la suscripción automáticamente.',
            [LocaleEnum.kk]: 'Автоматтық түзетілген жазбалықта қате пайда болды.',
            [LocaleEnum.uz]: 'Avtomatik obuna yangilanishi da xatolik yuz berdi.',
            [LocaleEnum.de]: 'Beim automatischen Verlängern des Abos ist ein Fehler aufgetreten.',
            [LocaleEnum.fr]: "Une erreur s'est produite lors du renouvellement automatique de l'abonnement.",
            [LocaleEnum.it]: "Si è verificato un errore durante il rinnovo automatico dell'abbonamento.",
        }[locale];
    }

    public getUrl(): string {
        return this.getTariffUrl();
    }
}
