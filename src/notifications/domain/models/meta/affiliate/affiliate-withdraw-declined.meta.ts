import { LocaleEnum } from '@prisma/client';

import { AffiliateWithdrawMeta } from '../abstract/affiliate-withdraw-meta.abstract';

export class AffiliateWithdrawDeclinedMeta extends AffiliateWithdrawMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        return {
            [LocaleEnum.ru]: 'Вывод средств партнера отклонен',
            [LocaleEnum.en]: "Partner's withdrawal has been declined",
            [LocaleEnum.es]: 'El retiro del socio ha sido rechazado',
            [LocaleEnum.kk]: 'Партнердің шығарымы қабылданбаған',
            [LocaleEnum.uz]: "Hamkor to'lov chiqimlari rad etildi",
            [LocaleEnum.de]: 'Die Auszahlung des Partners wurde abgelehnt',
            [LocaleEnum.fr]: 'Le retrait du partenaire a été refusé',
            [LocaleEnum.it]: 'Il prelievo del partner è stato rifiutato',
        }[locale];
    }

    public getUrl(): string {
        return this.getPartnerUrl();
    }
}
