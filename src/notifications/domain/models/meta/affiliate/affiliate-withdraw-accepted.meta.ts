import { LocaleEnum } from '@prisma/client';

import { AffiliateWithdrawMeta } from '../abstract/affiliate-withdraw-meta.abstract';

export class AffiliateWithdrawAcceptedMeta extends AffiliateWithdrawMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        return {
            [LocaleEnum.ru]: 'Вывод средств партнера подтвержден',
            [LocaleEnum.en]: `The partner's withdrawal has been confirmed`,
            [LocaleEnum.es]: 'El retiro del socio ha sido confirmado',
            [LocaleEnum.kk]: 'Партнердің шығарымы растауленді',
            [LocaleEnum.uz]: "Hamkor to'lov chiqimlari tasdiqlandi",
            [LocaleEnum.de]: 'Der Auszahlungsantrag des Partners wurde bestätigt',
            [LocaleEnum.fr]: 'Le retrait du partenaire a été confirmé',
            [LocaleEnum.it]: 'Il prelievo del partner è stato confermato',
        }[locale];
    }

    public getUrl(): string {
        return this.getPartnerUrl();
    }
}
