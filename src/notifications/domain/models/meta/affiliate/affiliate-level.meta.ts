import { LocaleEnum } from '@prisma/client';
import { z } from 'zod';

import { NotificationMeta } from '../abstract/notification-meta.abstract';
import { PartnerSchema } from '../meta.schema';

const AffiliateLevelSchema = z.object({
    partner: PartnerSchema,
    url: z.string().url('Ссылка для уведомления должна быть валидным URL'),
});
type AffiliateLevelMetaType = z.infer<typeof AffiliateLevelSchema>;

export class AffiliateLevelMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, AffiliateLevelSchema);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { partner } = this.meta as AffiliateLevelMetaType;
        return {
            [LocaleEnum.ru]: `Ваш статус партнерской программе - <b>"${partner.level.name}"</b>`,
            [LocaleEnum.en]: `Your status in the partner program is <b>"${partner.level.name}"</b>`,
            [LocaleEnum.es]: `Tu estado en el programa de afiliados es <b>"${partner.level.name}"</b>`,
            [LocaleEnum.kk]: `Сіздің партнерлік программадағы статустыңыз - <b>"${partner.level.name}"</b>`,
            [LocaleEnum.uz]: `Sizning hamkor dasturidagi statusingiz - <b>"${partner.level.name}"</b>`,
            [LocaleEnum.de]: `Dein Status im Partnerprogramm ist <b>"${partner.level.name}"</b>`,
            [LocaleEnum.fr]: `Votre statut dans le programme partenaire est <b>"${partner.level.name}"</b>`,
            [LocaleEnum.it]: `Il tuo stato nel programma partner è <b>"${partner.level.name}"</b>`,
        }[locale];
    }

    public getUrl(): string {
        return this.getPartnerUrl();
    }
}
