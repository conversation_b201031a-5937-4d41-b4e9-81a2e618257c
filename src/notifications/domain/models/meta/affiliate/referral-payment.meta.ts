import { LocaleEnum } from '@prisma/client';
import { z } from 'zod';

import { NotificationMeta } from '../abstract/notification-meta.abstract';
import { PartnerSchema, ReferralPaymentSchema } from '../meta.schema';

const ReferralPaymentMetaSchema = z.object({
    referralPayment: ReferralPaymentSchema,
    partner: PartnerSchema,
    url: z.string().url('Ссылка для уведомления должна быть валидным URL'),
});

export class ReferralPaymentMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, ReferralPaymentMetaSchema);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        return {
            [LocaleEnum.ru]: `Новая оплата по вашей партнерской ссылке`,
            [LocaleEnum.en]: `New payment for your referral link`,
            [LocaleEnum.es]: `Nuevo pago por tu enlace de referencia`,
            [LocaleEnum.kk]: `Сіздің партнерлік сілтемегі бойынша жаңа төлеу`,
            [LocaleEnum.uz]: `Sizning referal havolingiz bo'yicha yangi to'lov`,
            [LocaleEnum.de]: `Neue Zahlung für deinen Referral-Link`,
            [LocaleEnum.fr]: `Nouveau paiement pour votre lien de parrainage`,
            [LocaleEnum.it]: `Nuovo pagamento per il tuo link di riferimento`,
        }[locale];
    }

    public getUrl(): string {
        return this.getPartnerUrl();
    }
}
