import { LocaleEnum } from '@prisma/client';

import { AffiliateRequisitesMeta } from '../abstract/affiliate-requisites-meta.abstract';

export class AffiliateRequisitesDeclinedMeta extends AffiliateRequisitesMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        return {
            [LocaleEnum.ru]: 'Верификация партнера отклонена',
            [LocaleEnum.en]: 'The partner verification has been declined',
            [LocaleEnum.es]: 'La verificación del socio ha sido rechazada',
            [LocaleEnum.kk]: 'Партнердің тексерілімі қабылданбаған',
            [LocaleEnum.uz]: 'Hamkor tekshiruvi rad etildi',
            [LocaleEnum.de]: 'Die Partnerverifizierung wurde abgelehnt',
            [LocaleEnum.fr]: 'La vérification du partenaire a été refusée',
            [LocaleEnum.it]: 'La verifica del partner è stata rifiutata',
        }[locale];
    }

    public getUrl(): string {
        return this.getPartnerUrl();
    }
}
