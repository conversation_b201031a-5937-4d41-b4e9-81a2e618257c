import { LocaleEnum } from '@prisma/client';

import { AffiliateRequisitesMeta } from '../abstract/affiliate-requisites-meta.abstract';

export class AffiliateRequisitesAcceptedMeta extends AffiliateRequisitesMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        return {
            [LocaleEnum.ru]: 'Верификация партнера пройдена',
            [LocaleEnum.en]: 'Partner verification successful',
            [LocaleEnum.es]: 'Verificación del socio exitosa',
            [LocaleEnum.kk]: 'Партнердің тексерілімі жеткілікті',
            [LocaleEnum.uz]: 'Hamkor tekshiruv muvaffaqiyatli yakunlandi',
            [LocaleEnum.de]: 'Partnerverifizierung erfolgreich',
            [LocaleEnum.fr]: 'Vérification du partenaire réussie',
            [LocaleEnum.it]: 'Verifica del partner avvenuta con successo',
        }[locale];
    }

    public getUrl(): string {
        return this.getPartnerUrl();
    }
}
