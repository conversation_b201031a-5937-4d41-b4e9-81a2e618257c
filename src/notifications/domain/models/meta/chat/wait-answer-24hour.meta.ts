import { LocaleEnum } from '@prisma/client';
import { z } from 'zod';

import { NotificationMeta, UserEmailParam } from '../abstract/notification-meta.abstract';
import { MetaAbstractSchema, StudentSchema } from '../meta.schema';

const WaitAnswer24HourMetaSchema = MetaAbstractSchema.omit({ url: true }).extend({
    student: StudentSchema,
});
type WaitAnswer24HourMetaType = z.infer<typeof WaitAnswer24HourMetaSchema>;

export class WaitAnswer24HourMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, WaitAnswer24HourMetaSchema);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { student } = this.meta as WaitAnswer24HourMetaType;
        return {
            [LocaleEnum.ru]: `Студент ${student.name} ждет ответа в чате более 24 часов`,
            [LocaleEnum.en]: `Student ${student.name} is waiting for an answer in the chat for 24 hours`,
            [LocaleEnum.es]: `El estudiante ${student.name} está esperando una respuesta en el chat durante más de 24 horas`,
            [LocaleEnum.kk]: `Оқушы ${student.name} сөйлеуде жауапты 24 сағаттан бері күтеді`,
            [LocaleEnum.uz]: `Talaba ${student.name} so'zlamada javobni 24 soatdan beri kuting`,
            [LocaleEnum.de]: `Der Schüler ${student.name} wartet seit 24 Stunden auf eine Antwort im Chat`,
            [LocaleEnum.fr]: `L'étudiant ${student.name} attend une réponse dans le chat depuis plus de 24 heures`,
            [LocaleEnum.it]: `Lo studente ${student.name} sta aspettando una risposta nella chat da più di 24 ore`,
        }[locale];
    }

    public getEmailTitle(userParams: UserEmailParam): string {
        const { locale } = userParams;
        return {
            [LocaleEnum.ru]: `Непрочитанные сообщения`,
            [LocaleEnum.en]: `Unread messages`,
            [LocaleEnum.es]: `Mensajes no leídos`,
            [LocaleEnum.kk]: `Оқылмаған хабарламалар`,
            [LocaleEnum.uz]: `Укитилмаган хабарлар`,
            [LocaleEnum.de]: `Ungelesene Nachrichten`,
            [LocaleEnum.fr]: `Messages non lus`,
            [LocaleEnum.it]: `Messaggi non letti`,
        }[locale];
    }

    public getEmailText(userParams: UserEmailParam): string {
        const { locale } = userParams;
        return {
            [LocaleEnum.ru]: `Некоторые из учеников очень ждут вашего ответа`,
            [LocaleEnum.en]: `Some of your students are eagerly waiting for your response`,
            [LocaleEnum.es]: `Algunos de tus estudiantes están esperando con ansias tu respuesta`,
            [LocaleEnum.kk]: `Сіздің жауабыңызды бірнеше оқушылар өте күтіп жатыр`,
            [LocaleEnum.uz]: `Sizning javobingizni bir qancha talabalar kutinganligini`,
            [LocaleEnum.de]: `Einige Ihrer Schüler warten sehnsüchtig auf Ihre Antwort`,
            [LocaleEnum.fr]: `Certains de vos étudiants attendent votre réponse avec impatience`,
            [LocaleEnum.it]: `Alcuni dei tuoi studenti stanno aspettando la tua risposta con grande attesa`,
        }[locale];
    }

    public getEmailButtonText(locale: LocaleEnum): string {
        return {
            [LocaleEnum.ru]: 'Перейти в кабинет',
            [LocaleEnum.en]: 'Go to dashboard',
            [LocaleEnum.es]: 'Ir al panel de control',
            [LocaleEnum.kk]: 'Кабинетке өту',
            [LocaleEnum.uz]: 'Kabinetga o’tish',
            [LocaleEnum.de]: 'Zum Dashboard gehen',
            [LocaleEnum.fr]: 'Aller au tableau de bord',
            [LocaleEnum.it]: 'Vai al dashboard',
        }[locale];
    }

    public getUrl(): string {
        const { domain } = this.meta as WaitAnswer24HourMetaType;
        return domain;
    }
}
