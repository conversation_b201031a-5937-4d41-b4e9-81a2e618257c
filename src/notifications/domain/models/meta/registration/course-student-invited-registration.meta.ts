import { LocaleEnum } from '@prisma/client';

import { CourseAccessMeta, CourseAccessMetaType } from '../abstract/course-access-meta.abstract';
import { UserEmailParam } from '../abstract/notification-meta.abstract';

export class CourseStudentInviteAndRegisterMeta extends CourseAccessMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getEmailTitle(userParams: UserEmailParam): string {
        return {
            [LocaleEnum.ru]: 'Осталось установить пароль и войти в аккаунт',
            [LocaleEnum.en]: 'Set a password and log in to your account',
            [LocaleEnum.es]: 'Establece una contraseña e inicia sesión en tu cuenta',
            [LocaleEnum.kk]: 'Құпия сөзді орнату керек және акаунтқа кіру',
            [LocaleEnum.uz]: 'Parol o`rnatish va hisobingizga kiring',
            [LocaleEnum.de]: 'Legen Sie ein Passwort fest und melden Sie sich bei Ihrem Konto an',
            [LocaleEnum.fr]: 'Définissez un mot de passe et connectez-vous à votre compte',
            [LocaleEnum.it]: 'Imposta una password e accedi al tuo account',
        }[userParams.locale];
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { course } = this.meta as CourseAccessMetaType;
        return {
            [LocaleEnum.ru]: `Вам открыт доступ к курсу <b>"${course.name}".</b></br>Чтобы войти в аккаунт и начать обучение, нажмите на кнопку ниже и установите пароль`,
            [LocaleEnum.en]: `You have been invited to the course <b>"${course.name}".</b></br>To log in to your account and start learning, click the button below and set a password`,
            [LocaleEnum.es]: `Se te ha concedido acceso al curso <b>"${course.name}".</b></br>Para iniciar sesión en tu cuenta y comenzar a aprender, haz clic en el botón de abajo y establece una contraseña`,
            [LocaleEnum.kk]: `Сізге курсқа <b>"${course.name}"</b> кіру мүмкіндігі ашық.</br>Аккаунтқа кіру және оқуды бастау үшін, төмендегі батырманы басып, құпия сөзді орнатыңыз`,
            [LocaleEnum.uz]: `Sizga kursga <b>"${course.name}"</b> kirish ochilgan.</br>Hisobingizga kirish va o'rganishni boshlash uchun, quyidagi tugmani bosing va parol o'rnating`,
            [LocaleEnum.de]: `Sie haben Zugriff auf den Kurs <b>"${course.name}"</b> erhalten.</br>Um sich bei Ihrem Konto anzumelden und mit dem Lernen zu beginnen, klicken Sie auf die Schaltfläche unten und legen Sie ein Passwort fest`,
            [LocaleEnum.fr]: `Vous avez reçu un accès au cours <b>"${course.name}".</b></br>Pour vous connecter à votre compte et commencer à apprendre, cliquez sur le bouton ci-dessous et définissez un mot de passe`,
            [LocaleEnum.it]: `Ti è stato concesso l'accesso al corso <b>"${course.name}".</b></br>Per accedere al tuo account e iniziare ad imparare, clicca sul pulsante qui sotto e imposta una password`,
        }[locale];
    }

    public getEmailButtonText(locale: LocaleEnum): string {
        return {
            [LocaleEnum.ru]: 'Войти в аккаунт',
            [LocaleEnum.en]: 'Log in to your account',
            [LocaleEnum.es]: 'Inicia sesión en tu cuenta',
            [LocaleEnum.kk]: 'Акаунтқа кіру',
            [LocaleEnum.uz]: 'Hisobingizga kiring',
            [LocaleEnum.de]: 'Anmelden bei Ihrem Konto',
            [LocaleEnum.fr]: 'Connectez-vous à votre compte',
            [LocaleEnum.it]: 'Accedi al tuo account',
        }[locale];
    }

    public getUrl(): string {
        return this.getCourseUrl();
    }
}
