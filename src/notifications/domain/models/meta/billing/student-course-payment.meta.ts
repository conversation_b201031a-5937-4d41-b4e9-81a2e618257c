import { LocaleEnum } from '@prisma/client';
import { z } from 'zod';

import { NotificationMeta } from '../abstract/notification-meta.abstract';
import { CourseSchema, GroupSchema, MetaAbstractSchema, StudentSchema } from '../meta.schema';

const StudentCoursePaymentSchema = MetaAbstractSchema.extend({
    student: StudentSchema,
    course: CourseSchema,
    group: GroupSchema,
});
type StudentCoursePaymentMetaType = z.infer<typeof StudentCoursePaymentSchema>;

export class StudentCoursePaymentMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, StudentCoursePaymentSchema);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { course, student } = this.meta as StudentCoursePaymentMetaType;
        const studentName = student.name ? `<b>"${student.name}"</b>` : '';
        return {
            [LocaleEnum.ru]: `Ученик ${studentName} оплатил курс <b>"${course.name}"</b>`,
            [LocaleEnum.en]: `The student ${studentName} has paid for the course <b>"${course.name}"</b>`,
            [LocaleEnum.es]: `El estudiante ${studentName} ha pagado el curso <b>"${course.name}"</b>`,
            [LocaleEnum.kk]: `Оқушы ${studentName} курсті төледі <b>"${course.name}"</b>`,
            [LocaleEnum.uz]: `Talaba ${studentName} kurs uchun to'lov amalga oshirdi <b>"${course.name}"</b>`,
            [LocaleEnum.de]: `Der Schüler ${studentName} hat den Kurs bezahlt <b>"${course.name}"</b>`,
            [LocaleEnum.fr]: `L'étudiant ${studentName} a payé le cours <b>"${course.name}"</b>`,
            [LocaleEnum.it]: `Lo studente ${studentName} ha pagato il corso <b>"${course.name}"</b>`,
        }[locale];
    }

    public getUrl(): string {
        return this.getBalanceUrl();
    }
}
