import { LocaleEnum } from '@prisma/client';
import { z } from 'zod';

import { NotificationMeta } from '../abstract/notification-meta.abstract';
import { CourseSchema, HomeworkUserSchema, LessonSchema, MetaAbstractSchema, TeacherSchema } from '../meta.schema';

const TeacherFeedbackSchema = MetaAbstractSchema.omit({ url: true }).extend({
    course: CourseSchema,
    lesson: LessonSchema,
    teacher: TeacherSchema,
    homeworkUser: HomeworkUserSchema,
});
type TeacherFeedbackMetaType = z.infer<typeof TeacherFeedbackSchema>;

export class TeacherFeedbackMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, TeacherFeedbackSchema);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { lesson } = this.meta as TeacherFeedbackMetaType;
        return {
            [LocaleEnum.ru]: `Новый ответ преподавателя по заданию <b>"${lesson.name}"</b>`,
            [LocaleEnum.en]: `New teacher's answer to the task <b>"${lesson.name}"</b>`,
            [LocaleEnum.es]: `Nueva respuesta del profesor para la tarea <b>"${lesson.name}"</b>`,
            [LocaleEnum.kk]: `Тапсырма бойынша мұғалімнің жаңа жауабы <b>"${lesson.name}"</b>`,
            [LocaleEnum.uz]: `Vazifa bo'yicha o'qituvchining yangi javobi <b>"${lesson.name}"</b>`,
            [LocaleEnum.de]: `Neue Antwort des Lehrers zur Aufgabe <b>"${lesson.name}"</b>`,
            [LocaleEnum.fr]: `Nouvelle réponse de l'enseignant pour la tâche <b>"${lesson.name}"</b>`,
            [LocaleEnum.it]: `Nuova risposta dell'insegnante per l'attività <b>"${lesson.name}"</b>`,
        }[locale];
    }
}
