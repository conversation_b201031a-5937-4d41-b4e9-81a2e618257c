import { LocaleEnum } from '@prisma/client';

import { HomeworkCronMeta, HomeworkCronMetaType } from '../abstract/homework-cron-meta.abstract';
import { UserEmailParam } from '../abstract/notification-meta.abstract';

export class TeacherFeedbackCronMeta extends HomeworkCronMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        return {
            [LocaleEnum.ru]: 'Ваши домашние работы проверены',
            [LocaleEnum.en]: 'The teacher has submitted feedback on the homework',
            [LocaleEnum.es]: 'El profesor ha proporcionado retroalimentación sobre los deberes',
            [LocaleEnum.kk]: 'Мұғалім сіздің үй тапсырмаларыңыз бойынша байланыс берді',
            [LocaleEnum.uz]: 'O`qituvchi uy vazifalaringiz bo`yicha muvofaqiyat yubordi',
            [LocaleEnum.de]: 'Der Lehrer hat Feedback zu den Hausaufgaben abgegeben',
            [LocaleEnum.fr]: "L'enseignant a soumis des commentaires sur les devoirs",
            [LocaleEnum.it]: 'L’insegnante ha fornito un feedback sui compiti a casa',
        }[locale];
    }

    public getEmailTitle(userParams: UserEmailParam): string {
        const { locale } = userParams;
        return this.getMessageText(locale);
    }

    public getEmailText(userParams: UserEmailParam): string {
        const { locale } = userParams;
        return {
            [LocaleEnum.ru]: `Вы можете войти на платформу и посмотреть ответы к вашим домашним работам.`,
            [LocaleEnum.en]: `You can log in to the platform and view the answers to your homework assignments.`,
            [LocaleEnum.es]: `Puedes acceder a la plataforma y ver las respuestas a tus tareas.`,
            [LocaleEnum.kk]: `Сіз платформадағы енгізіп, сіздің басқармалық тапсырмаларына жауаптарды қарауға болады.`,
            [LocaleEnum.uz]: `Siz platformaga kirib, sizning uyingizdagi vazifalarga berilgan javonlarni ko'ra olasiz.`,
            [LocaleEnum.de]: `Sie können sich auf der Plattform anmelden und die Antworten zu Ihren Hausaufgaben anzeigen.`,
            [LocaleEnum.fr]: `Vous pouvez vous connecter à la plateforme et consulter les réponses à vos devoirs maison.`,
            [LocaleEnum.it]: `Puoi accedere alla piattaforma e visualizzare le risposte ai tuoi compiti a casa.`,
        }[locale];
    }

    public getEmailButtonText(locale: LocaleEnum): string {
        return {
            [LocaleEnum.ru]: 'Перейти в кабинет',
            [LocaleEnum.en]: 'Go to dashboard',
            [LocaleEnum.es]: 'Ir al panel de control',
            [LocaleEnum.kk]: 'Кабинетке өту',
            [LocaleEnum.uz]: 'Kabinetga o’tish',
            [LocaleEnum.de]: 'Zum Dashboard gehen',
            [LocaleEnum.fr]: 'Aller au tableau de bord',
            [LocaleEnum.it]: 'Vai al dashboard',
        }[locale];
    }

    public getUrl(): string {
        const { domain } = this.meta as HomeworkCronMetaType;
        return domain;
    }
}
