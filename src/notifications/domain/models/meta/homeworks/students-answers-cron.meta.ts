import { LocaleEnum } from '@prisma/client';

import { HomeworkCronMeta, HomeworkCronMetaType } from '../abstract/homework-cron-meta.abstract';
import { UserEmailParam } from '../abstract/notification-meta.abstract';

export class StudentsAnswersCronMeta extends HomeworkCronMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        return {
            [LocaleEnum.ru]: 'Ученики прислали работы на проверку!',
            [LocaleEnum.en]: 'Students have submitted their work for review!',
            [LocaleEnum.es]: '¡Los estudiantes han enviado sus trabajos para su revisión!',
            [LocaleEnum.kk]: 'Оқушылар жұмыстарын тексеру үшін жіберді!',
            [LocaleEnum.uz]: 'Talabalar vazifalarni tekshirish uchun yubordi!',
            [LocaleEnum.de]: 'Die Schüler haben ihre Arbeiten zur Überprüfung eingereicht!',
            [LocaleEnum.fr]: 'Les étudiants ont soumis leurs travaux pour examen!',
            [LocaleEnum.it]: 'Gli studenti hanno inviato i loro lavori per la verifica!',
        }[locale];
    }

    public getEmailTitle(userParams: UserEmailParam): string {
        const { locale } = userParams;
        return this.getMessageText(locale);
    }

    public getEmailText(userParams: UserEmailParam): string {
        const { locale } = userParams;
        return {
            [LocaleEnum.ru]: `Не забудьте проверить работы учеников на платформе!`,
            [LocaleEnum.en]: `Don't forget to check the students' work on the platform!`,
            [LocaleEnum.es]: `¡No olvides revisar los trabajos de los estudiantes en la plataforma!`,
            [LocaleEnum.kk]: `Студенттердің жұмыстарын платформада тексеріңіз шығарып алыңыз!`,
            [LocaleEnum.uz]: `Talabalar ishlari platformada tekshirilishi kerak!`,
            [LocaleEnum.de]: `Vergessen Sie nicht, die Arbeiten der Schüler auf der Plattform zu überprüfen!`,
            [LocaleEnum.fr]: `N'oubliez pas de vérifier les travaux des étudiants sur la plateforme !`,
            [LocaleEnum.it]: `Non dimenticare di controllare i lavori degli studenti sulla piattaforma!`,
        }[locale];
    }

    public getEmailButtonText(locale: LocaleEnum): string {
        return {
            [LocaleEnum.ru]: 'Перейти в кабинет',
            [LocaleEnum.en]: 'Go to dashboard',
            [LocaleEnum.es]: 'Ir al panel de control',
            [LocaleEnum.kk]: 'Кабинетке өту',
            [LocaleEnum.uz]: 'Kabinetga o’tish',
            [LocaleEnum.de]: 'Zum Dashboard gehen',
            [LocaleEnum.fr]: 'Aller au tableau de bord',
            [LocaleEnum.it]: 'Vai al dashboard',
        }[locale];
    }

    public getUrl(): string {
        const { domain } = this.meta as HomeworkCronMetaType;
        return domain;
    }
}
