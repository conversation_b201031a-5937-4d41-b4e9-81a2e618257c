import { LocaleEnum } from '@prisma/client';
import { z } from 'zod';

import { NotificationMeta } from '../abstract/notification-meta.abstract';
import { HomeworkUserSchema, LessonSchema, MetaAbstractSchema, StudentSchema } from '../meta.schema';

const StudentAnswerSchema = MetaAbstractSchema.extend({
    lesson: LessonSchema,
    student: StudentSchema,
    homeworkUser: HomeworkUserSchema,
});
type StudentAnswerMetaType = z.infer<typeof StudentAnswerSchema>;

export class StudentAnswerMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, StudentAnswerSchema);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { student, lesson } = this.meta as StudentAnswerMetaType;
        const studentName = student.name ? `<b>"${student.name}"</b>` : '';
        return {
            [LocaleEnum.ru]: `Ученик ${studentName} прислал ответ на задание <b>"${lesson.name}"</b>`,
            [LocaleEnum.en]: `The student ${studentName} has submitted an answer to the task <b>"${lesson.name}"</b>`,
            [LocaleEnum.es]: `El estudiante ${studentName} ha enviado una respuesta a la tarea <b>"${lesson.name}"</b>`,
            [LocaleEnum.kk]: `Оқушы ${studentName} тапсырмаға <b>"${lesson.name}"</b> жауап берді`,
            [LocaleEnum.uz]: `Talaba ${studentName} vazifaga <b>"${lesson.name}"</b> javob yubordi`,
            [LocaleEnum.de]: `Der Schüler ${studentName} hat eine Antwort zur Aufgabe <b>"${lesson.name}"</b> eingereicht`,
            [LocaleEnum.fr]: `L'étudiant ${studentName} a soumis une réponse à la tâche <b>"${lesson.name}"</b>`,
            [LocaleEnum.it]: `Lo studente ${studentName} ha inviato una risposta all'attività <b>"${lesson.name}"</b>`,
        }[locale];
    }

    public getUrl(): string {
        const { domain, homeworkUser } = this.meta as StudentAnswerMetaType;
        return `${domain}/school/homeworks?uuid=${homeworkUser.uuid}`;
    }
}
