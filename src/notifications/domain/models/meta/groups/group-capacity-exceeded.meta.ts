import { LocaleEnum } from '@prisma/client';
import { z } from 'zod';

import { NotificationMeta } from '../abstract/notification-meta.abstract';
import { CourseSchema, GroupSchema, MetaAbstractSchema } from '../meta.schema';

const GroupCapacityExceededSchema = MetaAbstractSchema.extend({
    course: CourseSchema,
    group: GroupSchema,
});
type GroupCapacityExceededMetaType = z.infer<typeof GroupCapacityExceededSchema>;

export class GroupCapacityExceededMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, GroupCapacityExceededSchema);
    }

    public getMessageText(locale: LocaleEnum = this.defaultLocale): string {
        const { course, group } = this.meta as GroupCapacityExceededMetaType;
        return {
            [LocaleEnum.ru]: `Закончились места в группе <b>"${group.name}"</b> курса <b>"${course.name}"</b>`,
            [LocaleEnum.en]: `There are no places left in the group <b>"${group.name}"</b> of the course <b>"${course.name}"</b>`,
            [LocaleEnum.es]: `No quedan lugares disponibles en el grupo <b>"${group.name}"</b> del curso <b>"${course.name}"</b>`,
            [LocaleEnum.kk]: `Курс <b>"${course.name}"</b> топында <b>"${group.name}"</b> орындар аяқталды`,
            [LocaleEnum.uz]: `Kurs <b>"${course.name}"</b> guruhida <b>"${group.name}"</b> o'rindiq lar tugadi`,
            [LocaleEnum.de]: `Es sind keine Plätze mehr frei in der Gruppe <b>"${group.name}"</b> des Kurses <b>"${course.name}"</b>`,
            [LocaleEnum.fr]: `Il n'y a plus de places disponibles dans le groupe <b>"${group.name}"</b> du cours <b>"${course.name}"</b>`,
            [LocaleEnum.it]: `Non ci sono più posti disponibili nel gruppo <b>"${group.name}"</b> del corso <b>"${course.name}"</b>`,
        }[locale];
    }

    public getUrl(): string {
        const { course, group, domain } = this.meta as GroupCapacityExceededMetaType;
        return `${domain}/students/courses/${course.id}/${group.id}`;
    }
}
