import { z } from 'zod';

import { CategorySchema, CourseSchema, MetaAbstractSchema } from '../meta.schema';
import { NotificationMeta } from './notification-meta.abstract';

const CourseAccessMetaSchema = MetaAbstractSchema.extend({
    course: CourseSchema.optional(),
    courses: z.array(CourseSchema).optional(),
    categories: z.array(CategorySchema).optional(),
});

export type CourseAccessMetaType = z.infer<typeof CourseAccessMetaSchema>;

export class CourseAccessMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, CourseAccessMetaSchema);
    }

    public getMessageText(): string {
        throw new Error('Not implemented');
    }
}
