import { z } from 'zod';

import { PartnerSchema, TransactionSchema } from '../meta.schema';
import { NotificationMeta } from './notification-meta.abstract';

const AffiliateWithdrawMetaSchema = z.object({
    transaction: TransactionSchema,
    partner: PartnerSchema,
    url: z.string().url('Ссылка для уведомления должна быть валидным URL'),
});

export class AffiliateWithdrawMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, AffiliateWithdrawMetaSchema);
    }

    public getMessageText(): string {
        throw new Error('Not implemented');
    }
}
