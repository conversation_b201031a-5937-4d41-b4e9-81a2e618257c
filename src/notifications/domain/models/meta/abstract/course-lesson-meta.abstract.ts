import { z } from 'zod';

import { CourseSchema, LessonSchema, MetaAbstractSchema } from '../meta.schema';
import { NotificationMeta } from './notification-meta.abstract';

const CourseLessonMetaSchema = MetaAbstractSchema.extend({
    course: CourseSchema,
    lesson: LessonSchema,
});
export type CourseLessonMetaType = z.infer<typeof CourseLessonMetaSchema>;

export class CourseLessonMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, CourseLessonMetaSchema);
    }

    public getMessageText(): string {
        throw new Error('Not implemented');
    }
}
