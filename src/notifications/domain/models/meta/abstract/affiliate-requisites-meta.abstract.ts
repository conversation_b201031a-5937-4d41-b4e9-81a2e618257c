import { z } from 'zod';

import { PartnerSchema, RequisitesSchema } from '../meta.schema';
import { NotificationMeta } from './notification-meta.abstract';

const RequisitesMetaSchema = z.object({
    requisites: RequisitesSchema,
    partner: PartnerSchema,
    url: z.string().url('Ссылка для уведомления должна быть валидным URL'),
});

export class AffiliateRequisitesMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, RequisitesMetaSchema);
    }

    public getMessageText(): string {
        throw new Error('Not implemented');
    }
}
