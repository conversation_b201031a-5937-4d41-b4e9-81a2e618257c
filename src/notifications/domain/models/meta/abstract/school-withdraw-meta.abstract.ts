import { MetaAbstractSchema, SchoolSchema, TransactionSchema } from '../meta.schema';
import { NotificationMeta } from './notification-meta.abstract';

const SchoolWithdrawMetaSchema = MetaAbstractSchema.extend({
    transaction: TransactionSchema,
    school: SchoolSchema,
});

export class SchoolWithdrawMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, SchoolWithdrawMetaSchema);
    }

    public getMessageText(): string {
        throw new Error('Not implemented');
    }
}
