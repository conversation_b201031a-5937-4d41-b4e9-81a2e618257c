import { z } from 'zod';

import { CourseSchema, LessonSchema, MetaAbstractSchema, WebinarSchema } from '../meta.schema';
import { NotificationMeta } from './notification-meta.abstract';

const WebinarMetaSchema = MetaAbstractSchema.extend({
    course: CourseSchema,
    lesson: LessonSchema,
    webinar: WebinarSchema,
});
export type WebinarMetaType = z.infer<typeof WebinarMetaSchema>;

export class WebinarMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, WebinarMetaSchema);
    }

    public getMessageText(): string {
        throw new Error('Not implemented');
    }
}
