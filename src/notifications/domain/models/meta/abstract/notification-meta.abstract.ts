import { LocaleEnum } from '@prisma/client';
import { formatDistance as formatDistanceFns } from 'date-fns';
import { de, enGB, es, fr, it, kk, ru, uz } from 'date-fns/locale';
import { format, formatInTimeZone } from 'date-fns-tz';
import { z } from 'zod';

import { WhiteLabelType } from '../meta.schema';
import { CourseLessonMetaType } from './course-lesson-meta.abstract';

export interface UserEmailParam {
    locale: LocaleEnum;
    unSubscribeUrl?: string;
    timezone?: string;
    timezoneAbbr?: string;
}

export abstract class NotificationMeta {
    protected readonly defaultLocale = LocaleEnum.ru;
    protected readonly defaultTimeZone = 'Europe/Moscow';
    protected readonly defaultTimeZoneAbbr = 'MSK';

    constructor(
        protected readonly meta: unknown,
        protected readonly createdAt: Date,
        protected readonly metaSchema: z.ZodType = z.object({}),
    ) {}

    protected formatDistance(startDate: Date, locale: LocaleEnum): string {
        const locales = {
            [LocaleEnum.ru]: ru,
            [LocaleEnum.en]: enGB,
            [LocaleEnum.es]: es,
            [LocaleEnum.kk]: kk,
            [LocaleEnum.uz]: uz,
            [LocaleEnum.de]: de,
            [LocaleEnum.fr]: fr,
            [LocaleEnum.it]: it,
        };
        const localeOptions = locales[locale] || locales[this.defaultLocale];

        return formatDistanceFns(this.createdAt, startDate, { addSuffix: false, locale: localeOptions });
    }

    protected formatDateWithTimeZone(date: Date | number | string, timeZone: string): string {
        return formatInTimeZone(date, timeZone, 'dd.MM.yyyy'); // при неправильной timeZone, выбросит ошибку
    }

    protected formatTimeWithTimeZone(date: Date | number | string, timeZone: string): string {
        return formatInTimeZone(date, timeZone, 'HH:mm'); // при неправильной timeZone, выбросит ошибку
    }

    protected getTimeZoneAbbr(date: Date | number | string, timeZone: string) {
        // Сначала пытаемся получить стандартную аббревиатуру
        const defaultAbbreviation = format(date, 'z', { timeZone }); //может использовать более общие обозначения, например, GMT+3,

        // если конкретная аббревиатура для данной временной зоны не определена в данных IANA
        const timeZoneAbbreviationMapping: Record<string, string> = {
            'Europe/Moscow': 'MSK',
            'Europe/Berlin': 'CET',
        };

        // Если нужна кастомная аббревиатура, используем маппинг
        return timeZoneAbbreviationMapping[timeZone] || defaultAbbreviation;
    }

    protected formatZodErrors(error: z.ZodError): string[] {
        return error.issues.map((issue) => {
            const path = issue.path.join('.');
            const message = issue.message;
            return `${path ? `${path}: ` : ''}${message}`;
        });
    }

    public safeValidate(): string[] {
        const validation = this.metaSchema.safeParse(this.meta);
        return validation.success ? [] : this.formatZodErrors(validation.error);
    }

    abstract getMessageText(locale: LocaleEnum): string;

    public getEmailTitle(userParams: UserEmailParam): string {
        void userParams;
        return null;
    }

    public getEmailText(userParams: UserEmailParam): string {
        return this.getMessageText(userParams.locale);
    }

    public getEmailButtonText(locale: LocaleEnum): string {
        return {
            [LocaleEnum.ru]: 'Подробнее',
            [LocaleEnum.en]: 'Details',
            [LocaleEnum.es]: 'Más detalles',
            [LocaleEnum.kk]: 'Білгілеу',
            [LocaleEnum.uz]: 'Batafsil',
            [LocaleEnum.de]: 'Weitere Details',
            [LocaleEnum.fr]: 'Détails',
            [LocaleEnum.it]: 'Dettagli',
        }[locale];
    }

    public getEmailData(userParams: UserEmailParam) {
        const meta = this.meta as { whiteLabel: WhiteLabelType; url?: string };
        return {
            // White label
            primaryColor: meta.whiteLabel.primaryColor,
            hideMobileAppLinks: meta.whiteLabel.hideMobileAppLinks,
            isWhiteLabel: meta.whiteLabel.isWhiteLabel,
            schoolLogoUrl: meta.whiteLabel.schoolLogoUrl,
            textOnly: meta.whiteLabel.textOnly,
            // Email data
            title: this.getEmailTitle(userParams),
            html: this.getEmailText(userParams),
            buttonText: this.getEmailButtonText(userParams.locale),
            // Links
            redirectUrl: this.getUrlFromMeta(),
            unSubscribeUrl: userParams?.unSubscribeUrl || null,
        };
    }

    public getUrl(): string {
        return null;
    }

    public getUrlFromMeta(): string {
        const meta = this.meta as { url: string };
        return meta.url;
    }

    protected getLessonUrl(): string {
        const { domain, course, lesson } = this.meta as CourseLessonMetaType;
        return `${domain}/course/${course.id}/${lesson.id}`;
    }

    protected getCourseUrl(): string {
        const { domain, course } = this.meta as CourseLessonMetaType;
        return `${domain}/course/${course.id}`;
    }

    protected getPartnerUrl(): string {
        const { domain } = this.meta as { domain: string };
        return `${domain}/partner`;
    }

    protected getBalanceUrl(): string {
        const { domain } = this.meta as { domain: string };
        return `${domain}/balance`;
    }

    protected getTariffUrl(): string {
        const { domain } = this.meta as { domain: string };
        return `${domain}/tariff/plan`;
    }

    protected getPlansUrl(): string {
        const { domain } = this.meta as { domain: string };
        return `${domain}/plans`;
    }

    public getUnsubscribeUrl(user: Pick<UserEmailParam, 'unSubscribeUrl'>): string {
        return user.unSubscribeUrl;
    }
}
