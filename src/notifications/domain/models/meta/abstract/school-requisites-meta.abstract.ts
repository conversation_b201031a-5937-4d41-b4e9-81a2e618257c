import { MetaAbstractSchema, RequisitesSchema, SchoolSchema } from '../meta.schema';
import { NotificationMeta } from './notification-meta.abstract';

const SchoolRequisitesMetaSchema = MetaAbstractSchema.extend({
    requisites: RequisitesSchema,
    school: SchoolSchema,
});

export class SchoolRequisitesMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, SchoolRequisitesMetaSchema);
    }

    public getMessageText(): string {
        throw new Error('Not implemented');
    }
}
