import { z } from 'zod';

import { MetaAbstractSchema } from '../meta.schema';
import { NotificationMeta } from './notification-meta.abstract';

const HomeworkCronMetaSchema = MetaAbstractSchema.omit({ url: true });
export type HomeworkCronMetaType = z.infer<typeof HomeworkCronMetaSchema>;

export class HomeworkCronMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, HomeworkCronMetaSchema);
    }

    public getMessageText(): string {
        throw new Error('Not implemented');
    }
}
