import { z } from 'zod';

import { CourseSchema, LessonSchema, MetaAbstractSchema } from '../meta.schema';
import { NotificationMeta } from './notification-meta.abstract';

const DeadlineMetaSchema = MetaAbstractSchema.extend({
    course: CourseSchema,
    lesson: LessonSchema,
});
export type DeadlineMetaType = z.infer<typeof DeadlineMetaSchema>;

export class DeadlineMeta extends NotificationMeta {
    constructor(meta: unknown, createdAt: Date) {
        super(meta, createdAt, DeadlineMetaSchema);
    }

    public getMessageText(): string {
        throw new Error('Not implemented');
    }
}
