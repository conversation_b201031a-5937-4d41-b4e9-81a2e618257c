import { ActionEnum, ClientEnum, LocaleEnum, NotificationGroupEnum, Prisma } from '@prisma/client';
import {
    EmailServiceSendEmailToManyContractNamespace,
    NotificationsCreateNotificationV2ContractNamespace,
    TelegramBotSendMessageContractNamespace,
    WebsocketNewNotificationContractNamespace,
} from '@skillspace/amqp-contracts';

import { isEmailAllowed } from '../../../config/emails.config';
import { NotificationSchemaService } from '../../application/services/notification-schema.service';
import { Identifier } from '../obj/identifier';
import { NotificationMeta } from './meta/abstract/notification-meta.abstract';
import { NotificationSettingsModel } from './notification-settings.model';

type User = NotificationsCreateNotificationV2ContractNamespace.User & {
    targetPlatforms: ClientEnum[];
    locale: LocaleEnum;
};

interface NotificationModelParams {
    school: NotificationsCreateNotificationV2ContractNamespace.School;
    users: User[];
    action: ActionEnum;
    group: NotificationGroupEnum;
    meta: NotificationsCreateNotificationV2ContractNamespace.Meta;
}

type NotificationCreateParams = Omit<NotificationModelParams, 'meta' | 'users'> & {
    meta: NotificationsCreateNotificationV2ContractNamespace.Meta;
    users: NotificationsCreateNotificationV2ContractNamespace.User[];
};

export class NotificationModelV2 {
    private readonly meta: NotificationsCreateNotificationV2ContractNamespace.Meta;
    private readonly users: (NotificationsCreateNotificationV2ContractNamespace.User & {
        targetPlatforms: ClientEnum[];
        locale: LocaleEnum;
    })[];
    private readonly school: NotificationsCreateNotificationV2ContractNamespace.School;

    private constructor(
        public readonly id: string,
        public readonly isSystem: boolean,
        private readonly metaSchema: NotificationMeta,
        private readonly params: NotificationModelParams,
    ) {
        this.meta = params.meta;
        this.users = params.users;
        this.school = params.school;
    }

    public static create(createInput: {
        schema: NotificationSchemaService;
        payload: NotificationCreateParams;
        userLocales: { userUuid: string; locale: LocaleEnum }[];
        anUserSettings?: NotificationSettingsModel[];
        createdAt: Date;
    }): NotificationModelV2 {
        const { schema, payload } = createInput;
        const isSystemNotification = schema.isSystemGroup(payload.group);
        if (!isSystemNotification && !createInput.anUserSettings) {
            throw new Error('Для не системных уведомлений необходимо передать настройки пользователей');
        }
        const action = payload.action;
        const NotificationMetaSchema = schema.getMetaSchema(action);
        const aNotificationMetaSchema = new NotificationMetaSchema(payload.meta, createInput.createdAt);

        const id = Identifier.generate().unwrap();
        const { meta, users, ...params } = payload;

        const getLocale = (user: { uuid: string }) =>
            createInput.userLocales.find(({ userUuid }) => userUuid === user.uuid)?.locale ||
            payload?.school?.language ||
            LocaleEnum.ru;

        if (isSystemNotification) {
            return new NotificationModelV2(id, isSystemNotification, aNotificationMetaSchema, {
                ...params,
                users: users.map((user) => ({
                    ...user,
                    targetPlatforms: [ClientEnum.PLATFORM, ClientEnum.BROWSER, ClientEnum.EMAIL],
                    locale: getLocale(user),
                })),
                meta,
            });
        } else {
            const userSettingsMap = new Map<string, NotificationSettingsModel>(
                createInput.anUserSettings.map((userProfile) => [userProfile.userUuid, userProfile]),
            );

            const userParams = users.map((user) => {
                const settings =
                    userSettingsMap.get(user.uuid) ?? NotificationSettingsModel.createDefault(user.uuid, schema);

                return {
                    ...user,
                    targetPlatforms: settings.getTargetPlatforms(payload.group),
                    locale: getLocale(user),
                };
            });

            return new NotificationModelV2(id, isSystemNotification, aNotificationMetaSchema, {
                ...params,
                users: userParams,
                meta,
            });
        }
    }

    public safeValidateMeta(): string[] {
        return this.metaSchema.safeValidate();
    }

    // Получение ссылки без протокола http
    private getUrl(): string {
        return this.metaSchema.getUrl();
    }

    private get whiteLabel() {
        return this.meta.whiteLabel;
    }

    private get action() {
        return this.params.action;
    }

    private text(locale: LocaleEnum = LocaleEnum.ru) {
        return this.metaSchema.getMessageText(locale);
    }

    private getName(schema: NotificationSchemaService, locale: LocaleEnum = LocaleEnum.ru): string {
        return schema.getMaps(locale).actions[this.action];
    }

    private get schoolUuid() {
        return this.school ? this.school.uuid : null;
    }

    public get notificationToSave(): Prisma.NotificationCreateInput {
        return {
            id: this.id,
            schoolUuid: this.params?.school ? this.params.school.uuid : null,
            meta: JSON.stringify(this.params.meta),
            group: this.params.group,
            action: this.params.action,
        };
    }

    public getUserNotificationsToSave(): Prisma.UserNotificationCreateManyInput[] {
        if (this.isCronNotification) {
            return [];
        }
        return this.params.users
            .map((user) => {
                return {
                    userUuid: user.uuid,
                    schoolUuid: this.schoolUuid,
                    notificationId: this.id,
                    clients: user.targetPlatforms,
                    group: this.params.group,
                };
            })
            .filter((userNotification) => userNotification.clients.length > 0);
    }

    public getEmailNotifications(
        schema: NotificationSchemaService,
    ): EmailServiceSendEmailToManyContractNamespace.RequestPayload[] {
        const usersToNotify = this.getUsersToNotify(ClientEnum.EMAIL);
        const whiteLabel = this.meta?.whiteLabel ? this.meta?.whiteLabel : {};
        return usersToNotify.map((user) => ({
            schoolUuid: this.schoolUuid,
            template: 'notification',
            subject: this.getName(schema, user?.locale),
            fromName: this.school?.name,
            fromEmail: this.school?.email,
            recipients: [user.email],
            body: {
                ...whiteLabel,
                title: this.metaSchema.getEmailTitle(user),
                html: this.metaSchema.getEmailText(user),
                buttonText: this.metaSchema.getEmailButtonText(user.locale),
                redirectUrl: this.metaSchema.getUrlFromMeta(),
                unSubscriberUrl: this.metaSchema.getUnsubscribeUrl(user),
            },
        }));
    }

    public getTelegramNotifications(): TelegramBotSendMessageContractNamespace.RequestPayload[] {
        const usersToNotify = this.getUsersToNotify(ClientEnum.TELEGRAM);
        return usersToNotify.map((user) => ({
            text: this.meta.url ? this.text(user?.locale) + ' ' + this.meta.url : this.text(user?.locale),
            unionAuthKey: user.unionAuthKey,
        }));
    }

    public getPlatformNotifications(): WebsocketNewNotificationContractNamespace.RequestPayload[] {
        const usersToNotify = this.getUsersToNotify(ClientEnum.PLATFORM);
        return usersToNotify.map((user) => {
            const payload: WebsocketNewNotificationContractNamespace.RequestPayload = {
                id: this.id,
                userId: user.id,
                schoolId: this.school?.id,
                userUuid: user.uuid,
                schoolUuid: this.schoolUuid,
                type: null,
                action: this.action as WebsocketNewNotificationContractNamespace.NotificationActionsEnum,
                text: this.text(user?.locale),
                meta: this.meta as WebsocketNewNotificationContractNamespace.Meta,
                viewed: false,
                createdAt: Date.now(),
                updatedAt: Date.now(),
            };
            if (payload.meta.webinar) {
                payload.meta.webinar.timezoneAbbr = user.timezoneAbbr;
            }
            return payload;
        });
    }

    public countPlatformNotifications() {
        return this.getUsersToNotify(ClientEnum.BROWSER).length;
    }

    private getUsersToNotify(target: ClientEnum): User[] {
        if (this.isCronNotification && target !== ClientEnum.EMAIL) {
            return [];
        }

        const usersToNotify = this.users.filter((user) => user.targetPlatforms.includes(target));

        if (target === ClientEnum.EMAIL) {
            if (this.notNotifyByEmail) {
                return [];
            }
            return usersToNotify.filter(({ email }) => isEmailAllowed(email));
        }
        return usersToNotify;
    }

    private get isCronNotification() {
        return (
            this.action === ActionEnum.ACTION_HOMEWORKS_TEACHER_SENT_CRON ||
            this.action === ActionEnum.ACTION_HOMEWORKS_STUDENTS_SENT_CRON
        );
    }

    private get notNotifyByEmail() {
        return (
            this.action === ActionEnum.ACTION_HOMEWORK_TEACHER_SENT ||
            this.action === ActionEnum.ACTION_HOMEWORK_STUDENT_SENT ||
            this.action === ActionEnum.ACTION_TARIFF_FEATURE_LIMIT_EXCEEDED
        );
    }
}
