import { ClientEnum, NotificationGroupEnum, NotificationSoundEnum, Prisma } from '@prisma/client';

import { removeNullProperties, removeUndefinedProperties } from '../../../shared/utils/mapper.utils';
import { NotificationSchemaService } from '../../application/services/notification-schema.service';
import { Identifier } from '../obj/identifier';
import { UserUuid } from '../obj/user-uuid';

export type GroupSettingsParams = {
    [K in keyof typeof NotificationGroupEnum]?: ClientEnum[];
};

export interface DeliverySettings {
    sound: NotificationSoundEnum;
    settings: GroupSettingsParams;
}

export type NotificationSettingsParams = DeliverySettings & {
    userUuid: string;
    version: number;
};

export class NotificationSettingsModel {
    public readonly id: string;
    public readonly version: number;

    public readonly userUuid: string;
    public readonly sound: NotificationSoundEnum;
    public readonly settings: GroupSettingsParams;

    private constructor(id: string, data: NotificationSettingsParams) {
        this.id = id;
        Object.assign(this, data);
    }

    public static createDefault(userUuid: string, schema: NotificationSchemaService): NotificationSettingsModel {
        const id = Identifier.generate().unwrap();
        return new NotificationSettingsModel(id, {
            version: 2,
            userUuid: UserUuid.create(userUuid).unwrap(),
            sound: NotificationSoundEnum.VOOP,
            settings: schema.getDefaultGroupSettings(),
        });
    }

    public update(dto: DeliverySettings): NotificationSettingsModel {
        const { id, version, userUuid, sound, settings: groups } = this;
        const filteredSettings = removeUndefinedProperties(removeNullProperties(dto.settings));
        const updatedSettings = groups;
        Object.keys(filteredSettings).forEach((group: keyof typeof NotificationGroupEnum) => {
            updatedSettings[group] = filteredSettings[group];
        });

        return new NotificationSettingsModel(id, {
            version,
            userUuid,
            sound: dto.sound || dto.sound === null ? dto.sound : sound,
            settings: updatedSettings,
        });
    }

    public unsetTelegram(): NotificationSettingsModel {
        const { id, version, userUuid, sound, settings } = this;
        const updatedSettings = {};
        Object.keys(settings).forEach((group: keyof typeof NotificationGroupEnum) => {
            updatedSettings[group] = settings[group].filter((client: ClientEnum) => client !== ClientEnum.TELEGRAM);
        });
        return new NotificationSettingsModel(id, {
            version,
            userUuid,
            sound,
            settings: updatedSettings,
        });
    }

    public static from(id: string, data: NotificationSettingsParams) {
        return new NotificationSettingsModel(id, {
            version: data.version,
            userUuid: data.userUuid,
            sound: data.sound,
            settings: data.settings,
        });
    }

    public getTargetPlatforms(group: NotificationGroupEnum): ClientEnum[] {
        return this.settings[group];
    }

    public getDefaultTargetPlatforms(schema: NotificationSchemaService, group: NotificationGroupEnum): ClientEnum[] {
        return schema.getDefaultGroupSettings()[group];
    }

    public get params(): Prisma.NotificationsSettingsCreateInput {
        return {
            version: 2,
            userUuid: this.userUuid,
            sound: this.sound,
            settings: this.settings,
        };
    }
}
