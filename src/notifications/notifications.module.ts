import { <PERSON>du<PERSON> } from '@nestjs/common';
import { applyOpentelemetryToProvider } from '@skillspace/tracing';

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './application/commands/mark-all-as-viewed';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>wed<PERSON> } from './application/commands/mark-as-unviewed';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './application/commands/mark-as-viewed';
import { SendNotificationCH } from './application/commands/send-notification';
import { SetNotificationSettingsCH } from './application/commands/set-notification-settings';
import { UnsetTelegramCH } from './application/commands/unset-telegram';
import { UserProvidedLocaleEventHandler } from './application/events/user-provided-locale';
import { UsersProvidedUnionAuthKeysEventHandler } from './application/events/users-provided-union-authkeys';
import { CountUserNotificationsQH } from './application/queries/count-user-notifications';
import { GetNotificationSettingsQH } from './application/queries/get-notification-settings';
import { GetUserNotificationsQH } from './application/queries/get-user-notifications';
import { NotificationSchemaService } from './application/services/notification-schema.service';
import { UserProfileProvider } from './infrastructure/providers/user-profile.provider';
import { EmailPublisher } from './infrastructure/publishers/emails.publisher';
import { TelegramPublisher } from './infrastructure/publishers/telegram.publisher';
import { WsPublisher } from './infrastructure/publishers/ws.publisher';
import { NotificationsRepository } from './infrastructure/repositories/notifications.repository';
import { SettingsRepository } from './infrastructure/repositories/settings.repository';
import { UserNotificationsRepository } from './infrastructure/repositories/user-notifications.repository';
import { NotificationsCron } from './infrastructure/scheduler/notifications.cron';
import {
    BROWSER_PUBLISHER,
    EMAIL_PUBLISHER,
    NOTIFICATIONS_REPOSITORY,
    SETTINGS_REPOSITORY,
    TELEGRAM_PUBLISHER,
    USER_NOTIFICATIONS_REPOSITORY,
    USER_PROFILE_PROVIDER,
} from './injects';
import { NotificationsConsumer } from './presentation/notifications.consumer';
import { SettingsConsumer } from './presentation/settings.consumer';
import { SettingsResolver } from './presentation/settings.resolver';
import { UserNotificationsResolver } from './presentation/user-notifications.resolver';

const API = [SettingsResolver, UserNotificationsResolver, SettingsConsumer, NotificationsConsumer];

const PROVIDERS = [
    NotificationSchemaService,
    NotificationsCron,
    {
        provide: USER_PROFILE_PROVIDER,
        useClass: UserProfileProvider,
    },
];

const REPOSITORIES = [
    { provide: USER_NOTIFICATIONS_REPOSITORY, useClass: UserNotificationsRepository },
    { provide: NOTIFICATIONS_REPOSITORY, useClass: NotificationsRepository },
    { provide: SETTINGS_REPOSITORY, useClass: SettingsRepository },
];
const PUBLISHERS = [
    { provide: EMAIL_PUBLISHER, useClass: EmailPublisher },
    { provide: TELEGRAM_PUBLISHER, useClass: TelegramPublisher },
    { provide: BROWSER_PUBLISHER, useClass: WsPublisher },
];
const COMMAND_HANDLERS = [
    SendNotificationCH,
    SetNotificationSettingsCH,
    MarkAsUnViewedCH,
    MarkAsViewedCH,
    MarkAllAsViewedCH,
    UnsetTelegramCH,
];

const EVENT_HANDLERS = [UsersProvidedUnionAuthKeysEventHandler, UserProvidedLocaleEventHandler];

const QUERY_HANDLERS = [GetNotificationSettingsQH, GetUserNotificationsQH, CountUserNotificationsQH];

@Module({
    providers: [
        ...API,
        ...PROVIDERS,
        ...PUBLISHERS,
        ...REPOSITORIES,
        ...COMMAND_HANDLERS,
        ...QUERY_HANDLERS,
        ...EVENT_HANDLERS,
    ].map((provider) => applyOpentelemetryToProvider(provider)),

    exports: REPOSITORIES,
})
export class NotificationsModule {}
