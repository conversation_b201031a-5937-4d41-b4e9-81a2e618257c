import { ActionEnum, NotificationGroupEnum } from '@prisma/client';

import { ActionDict } from '../dictionaries/actions.dict';
import { SettingsDict } from '../dictionaries/settings.dict';
import { UserRoleEnum } from '../shared/enums/user.enum';
import { NotificationMeta } from './domain/models/meta/abstract/notification-meta.abstract';
import { StudentCourseFinishedMeta } from './domain/models/meta/achivements/student-course-finished.meta';
import { AffiliateLevelMeta } from './domain/models/meta/affiliate/affiliate-level.meta';
import { AffiliateRequisitesAcceptedMeta } from './domain/models/meta/affiliate/affiliate-requisites-accepted.meta';
import { AffiliateRequisitesDeclinedMeta } from './domain/models/meta/affiliate/affiliate-requisites-declined.meta';
import { AffiliateWithdrawAcceptedMeta } from './domain/models/meta/affiliate/affiliate-withdraw-accepted.meta';
import { AffiliateWithdrawDeclinedMeta } from './domain/models/meta/affiliate/affiliate-withdraw-declined.meta';
import { ReferralPaymentMeta } from './domain/models/meta/affiliate/referral-payment.meta';
import { StudentCoursePaymentMeta } from './domain/models/meta/billing/student-course-payment.meta';
import { WaitAnswer24HourMeta } from './domain/models/meta/chat/wait-answer-24hour.meta';
import { CourseAccessBlockMeta } from './domain/models/meta/courses/course-acces-block.meta';
import { CourseAccessEndMeta } from './domain/models/meta/courses/course-acces-end.meta';
import { CourseAccessExpiry3DaysMeta } from './domain/models/meta/courses/course-acces-expiry-3-days.meta';
import { CourseLessonStudentInvitedMeta } from './domain/models/meta/courses/course-lesson-student-invited.meta';
import { CourseNewLessonMeta } from './domain/models/meta/courses/course-new-lesson.meta';
import { CourseNewModuleMeta } from './domain/models/meta/courses/course-new-module.meta';
import { CourseStudentInvited as CourseStudentInvitedMeta } from './domain/models/meta/courses/course-student-invited.meta';
import { DeadlineExpiredMeta } from './domain/models/meta/deadlines/deadline-expired.meta';
import { UpcomingDeadline1DayMeta } from './domain/models/meta/deadlines/upcoming-deadline-1-day.meta';
import { UpcomingDeadline3DaysMeta } from './domain/models/meta/deadlines/upcoming-deadline-3-days.meta';
import { GroupCapacityExceededMeta } from './domain/models/meta/groups/group-capacity-exceeded.meta';
import { StudentAnswerMeta } from './domain/models/meta/homeworks/student-answer.meta';
import { StudentsAnswersCronMeta } from './domain/models/meta/homeworks/students-answers-cron.meta';
import { TeacherFeedbackMeta } from './domain/models/meta/homeworks/teacher-feedback.meta';
import { TeacherFeedbackCronMeta } from './domain/models/meta/homeworks/teacher-feedback-cron.meta';
import { CourseStudentInviteAndRegisterMeta } from './domain/models/meta/registration/course-student-invited-registration.meta';
import { DiscountExpiredMeta } from './domain/models/meta/school-settings/discount-expired.meta';
import { RecurrentPaymentFailureMeta } from './domain/models/meta/subscription/recurrent-payment-failure.meta';
import { SubscriptionExpiredMeta } from './domain/models/meta/subscription/subscription-expired.meta';
import { UpcomingSubscriptionEnd1DayMeta } from './domain/models/meta/subscription/upcoming-subscription-end-1day.meta2';
import { UpcomingSubscriptionEnd3DaysMeta } from './domain/models/meta/subscription/upcoming-subscription-end-3days.meta';
import { UpcomingSubscriptionEnd7DaysMeta } from './domain/models/meta/subscription/upcoming-subscription-end-7days.meta';
import { FeatureLimitExceededMeta } from './domain/models/meta/tariffs/feature-limit-exceeded.meta';
import { SpeakerUpcomingWebinarMeta } from './domain/models/meta/webinars/speaker-upcoming-webinar.meta';
import { StudentUpcomingWebinarMeta } from './domain/models/meta/webinars/student-upcoming-webinar.meta';
import { WebinarStartStudentMeta } from './domain/models/meta/webinars/webinar-start-student.meta';
import { SchoolRequisitesAcceptedMeta } from './domain/models/meta/withdraw/school-requisites-accepted.meta';
import { SchoolRequisitesDeclinedMeta } from './domain/models/meta/withdraw/school-requisites-declined.meta';
import { SchoolWithdrawAcceptedMeta } from './domain/models/meta/withdraw/school-withdraw-accepted.meta';
import { SchoolWithdrawDeclinedMeta } from './domain/models/meta/withdraw/school-withdraw-declined.meta';

export interface Localization {
    ru: string;
    en: string;
    es: string;
    kk: string;
    uz: string;
    de: string;
    fr: string;
    it: string;
}

export type MetaSchemaConstructor = new (meta: unknown, createdAt: Date) => NotificationMeta;

export interface ActionSchemaType {
    /** Код события */
    code: ActionEnum;
    /** Название события */
    title: Localization;
    /** Схема события */
    schema: MetaSchemaConstructor;
}

export interface GroupSchemaType {
    /** Название группы настроек */
    title: Localization;
    /** Код группы настроек */
    code: NotificationGroupEnum;
    /** Кому доступны настройки */
    users: UserRoleEnum[];
    /** События, которые входят в группу настроек */
    actions: ActionSchemaType[];
}

export const NOTIFICATION_GROUPS: GroupSchemaType[] = [
    {
        code: NotificationGroupEnum.COURSES,
        title: SettingsDict.COURSES,
        users: [UserRoleEnum.student],
        actions: [
            {
                code: ActionEnum.ACTION_COURSE_PUBLISHED_NEW_LESSON,
                title: ActionDict.ACTION_COURSE_PUBLISHED_NEW_LESSON,
                schema: CourseNewLessonMeta,
            },
            {
                code: ActionEnum.ACTION_COURSE_ACCESS_STUDENT_ACCESS_END,
                title: ActionDict.ACTION_COURSE_ACCESS_STUDENT_ACCESS_END,
                schema: CourseAccessEndMeta,
            },
            {
                code: ActionEnum.ACTION_COURSE_ACCESS_STUDENT_ACCESS_END_3_DAYS,
                title: ActionDict.ACTION_COURSE_ACCESS_STUDENT_ACCESS_END_3_DAYS,
                schema: CourseAccessExpiry3DaysMeta,
            },
            {
                code: ActionEnum.ACTION_COURSE_ACCESS_STUDENT_NEW_LESSON,
                title: ActionDict.ACTION_COURSE_ACCESS_STUDENT_NEW_LESSON,
                schema: CourseLessonStudentInvitedMeta,
            },
            {
                code: ActionEnum.ACTION_COURSE_ACCESS_STUDENT_NEW_STEP,
                title: ActionDict.ACTION_COURSE_ACCESS_STUDENT_NEW_STEP,
                schema: CourseNewModuleMeta,
            },
            {
                code: ActionEnum.ACTION_COURSE_ACCESS_STUDENT_INVITE,
                title: ActionDict.ACTION_COURSE_ACCESS_STUDENT_INVITE,
                schema: CourseStudentInvitedMeta,
            },
            {
                code: ActionEnum.ACTION_COURSE_ACCESS_STUDENT_EXPEL,
                title: ActionDict.ACTION_COURSE_ACCESS_STUDENT_EXPEL,
                schema: CourseAccessBlockMeta,
            },
        ],
    },
    {
        code: NotificationGroupEnum.GROUP_STUDENT_LIMIT_OVER,
        title: SettingsDict.GROUP_STUDENT_LIMIT_OVER,
        users: [UserRoleEnum.teacher],
        actions: [
            {
                code: ActionEnum.ACTION_COURSE_ACCESS_GROUP_STUDENT_LIMIT_OVER,
                title: ActionDict.ACTION_COURSE_ACCESS_GROUP_STUDENT_LIMIT_OVER,
                schema: GroupCapacityExceededMeta,
            },
        ],
    },
    {
        code: NotificationGroupEnum.COURSE_STUDENT_FINISHED,
        title: SettingsDict.COURSE_STUDENT_FINISHED,
        users: [UserRoleEnum.teacher],
        actions: [
            {
                code: ActionEnum.ACTION_COURSE_STUDENT_FINISH_COURSE,
                title: ActionDict.ACTION_COURSE_STUDENT_FINISH_COURSE,
                schema: StudentCourseFinishedMeta,
            },
        ],
    },
    {
        code: NotificationGroupEnum.WEBINARS,
        title: SettingsDict.WEBINARS,
        users: [UserRoleEnum.student, UserRoleEnum.teacher],
        actions: [
            {
                code: ActionEnum.ACTION_COURSE_UPCOMING_WEBINAR_STUDENT,
                title: ActionDict.ACTION_COURSE_UPCOMING_WEBINAR_STUDENT,
                schema: StudentUpcomingWebinarMeta,
            },
            {
                code: ActionEnum.ACTION_COURSE_WEBINAR_START_STUDENT,
                title: ActionDict.ACTION_COURSE_WEBINAR_START_STUDENT,
                schema: WebinarStartStudentMeta,
            },
            {
                code: ActionEnum.ACTION_COURSE_UPCOMING_WEBINAR_TEACHER,
                title: ActionDict.ACTION_COURSE_UPCOMING_WEBINAR_TEACHER,
                schema: SpeakerUpcomingWebinarMeta,
            },
        ],
    },
    {
        code: NotificationGroupEnum.HOMEWORKS,
        title: SettingsDict.HOMEWORKS,
        users: [UserRoleEnum.student, UserRoleEnum.teacher],
        actions: [
            {
                code: ActionEnum.ACTION_HOMEWORK_STUDENT_SENT,
                title: ActionDict.ACTION_HOMEWORK_STUDENT_SENT,
                schema: StudentAnswerMeta,
            },
            {
                code: ActionEnum.ACTION_HOMEWORK_TEACHER_SENT,
                title: ActionDict.ACTION_HOMEWORK_TEACHER_SENT,
                schema: TeacherFeedbackMeta,
            },
            {
                code: ActionEnum.ACTION_HOMEWORKS_STUDENTS_SENT_CRON,
                title: ActionDict.ACTION_HOMEWORKS_STUDENTS_SENT_CRON,
                schema: StudentsAnswersCronMeta,
            },
            {
                code: ActionEnum.ACTION_HOMEWORKS_TEACHER_SENT_CRON,
                title: ActionDict.ACTION_HOMEWORKS_TEACHER_SENT_CRON,
                schema: TeacherFeedbackCronMeta,
            },
        ],
    },
    {
        code: NotificationGroupEnum.DEADLINES,
        title: SettingsDict.DEADLINES,
        users: [UserRoleEnum.student],
        actions: [
            {
                code: ActionEnum.ACTION_DEADLINE_UPCOMING_1_DAY,
                title: ActionDict.ACTION_DEADLINE_UPCOMING_1_DAY,
                schema: UpcomingDeadline1DayMeta,
            },
            {
                code: ActionEnum.ACTION_DEADLINE_UPCOMING_3_DAYS,
                title: ActionDict.ACTION_DEADLINE_UPCOMING_3_DAYS,
                schema: UpcomingDeadline3DaysMeta,
            },
            {
                code: ActionEnum.ACTION_DEADLINE_EXPIRED,
                title: ActionDict.ACTION_DEADLINE_EXPIRED,
                schema: DeadlineExpiredMeta,
            },
        ],
    },
    {
        code: NotificationGroupEnum.AFFILIATE_UPDATES,
        title: SettingsDict.AFFILIATE_UPDATES,
        users: [UserRoleEnum.student, UserRoleEnum.teacher],
        actions: [
            {
                code: ActionEnum.ACTION_AFFILIATE_REQUISITES_ACCEPTED,
                title: ActionDict.ACTION_AFFILIATE_REQUISITES_ACCEPTED,
                schema: AffiliateRequisitesAcceptedMeta,
            },
            {
                code: ActionEnum.ACTION_AFFILIATE_REQUISITES_DECLINED,
                title: ActionDict.ACTION_AFFILIATE_REQUISITES_DECLINED,
                schema: AffiliateRequisitesDeclinedMeta,
            },
            {
                code: ActionEnum.ACTION_AFFILIATE_WITHDRAW_ACCEPTED,
                title: ActionDict.ACTION_AFFILIATE_WITHDRAW_ACCEPTED,
                schema: AffiliateWithdrawAcceptedMeta,
            },
            {
                code: ActionEnum.ACTION_AFFILIATE_WITHDRAW_DECLINED,
                title: ActionDict.ACTION_AFFILIATE_WITHDRAW_DECLINED,
                schema: AffiliateWithdrawDeclinedMeta,
            },
            {
                code: ActionEnum.ACTION_AFFILIATE_LEVEL_UPDATE,
                title: ActionDict.ACTION_AFFILIATE_LEVEL_UPDATE,
                schema: AffiliateLevelMeta,
            },
            {
                code: ActionEnum.ACTION_AFFILIATE_REFERRAL_PAYMENT,
                title: ActionDict.ACTION_AFFILIATE_REFERRAL_PAYMENT,
                schema: ReferralPaymentMeta,
            },
        ],
    },
    {
        code: NotificationGroupEnum.SCHOOL_SETTINGS,
        title: SettingsDict.SCHOOL_SETTINGS,
        users: [UserRoleEnum.teacher],
        actions: [
            {
                code: ActionEnum.ACTION_SCHOOL_SETTINGS_DISCOUNT_EXPIRED,
                title: ActionDict.ACTION_SCHOOL_SETTINGS_DISCOUNT_EXPIRED,
                schema: DiscountExpiredMeta,
            },
        ],
    },
    {
        code: NotificationGroupEnum.CHAT,
        title: SettingsDict.CHAT,
        users: [UserRoleEnum.teacher],
        actions: [
            {
                code: ActionEnum.ACTION_CHAT_STUDENT_WAIT_ANSWER_24_HOUR,
                title: ActionDict.ACTION_CHAT_STUDENT_WAIT_ANSWER_24_HOUR,
                schema: WaitAnswer24HourMeta,
            },
        ],
    },
    {
        code: NotificationGroupEnum.BILLING,
        title: SettingsDict.BILLING,
        users: [UserRoleEnum.teacher],
        actions: [
            {
                code: ActionEnum.ACTION_BILLING_STUDENT_PAID_COURSE,
                title: ActionDict.ACTION_BILLING_STUDENT_PAID_COURSE,
                schema: StudentCoursePaymentMeta,
            },
        ],
    },
    {
        code: NotificationGroupEnum.WITHDRAWAL,
        title: SettingsDict.WITHDRAWAL,
        users: [UserRoleEnum.system],
        actions: [
            {
                code: ActionEnum.ACTION_BILLING_WITHDRAW_ACCEPTED,
                title: ActionDict.ACTION_BILLING_WITHDRAW_ACCEPTED,
                schema: SchoolWithdrawAcceptedMeta,
            },
            {
                code: ActionEnum.ACTION_BILLING_WITHDRAW_DECLINE,
                title: ActionDict.ACTION_BILLING_WITHDRAW_DECLINE,
                schema: SchoolWithdrawDeclinedMeta,
            },
            {
                code: ActionEnum.ACTION_BILLING_SCHOOL_REQUISITES_ACCEPTED,
                title: ActionDict.ACTION_BILLING_SCHOOL_REQUISITES_ACCEPTED,
                schema: SchoolRequisitesAcceptedMeta,
            },
            {
                code: ActionEnum.ACTION_BILLING_SCHOOL_REQUISITES_DECLINED,
                title: ActionDict.ACTION_BILLING_SCHOOL_REQUISITES_DECLINED,
                schema: SchoolRequisitesDeclinedMeta,
            },
        ],
    },
    {
        code: NotificationGroupEnum.TARIFFS,
        title: SettingsDict.TARIFFS,
        users: [UserRoleEnum.system],
        actions: [
            {
                code: ActionEnum.ACTION_TARIFF_FEATURE_LIMIT_EXCEEDED,
                title: ActionDict.ACTION_TARIFF_FEATURE_LIMIT_EXCEEDED,
                schema: FeatureLimitExceededMeta,
            },
        ],
    },
    {
        code: NotificationGroupEnum.SUBSCRIPTION,
        title: SettingsDict.SUBSCRIPTION,
        users: [UserRoleEnum.system],
        actions: [
            {
                code: ActionEnum.ACTION_TARIFF_SUBSCRIPTION_EXPIRED,
                title: ActionDict.ACTION_TARIFF_SUBSCRIPTION_EXPIRED,
                schema: SubscriptionExpiredMeta,
            },
            {
                code: ActionEnum.ACTION_TARIFF_SUBSCRIPTION_EXPIRATION_IN_7_DAYS,
                title: ActionDict.ACTION_TARIFF_SUBSCRIPTION_EXPIRATION_IN_7_DAYS,
                schema: UpcomingSubscriptionEnd7DaysMeta,
            },
            {
                code: ActionEnum.ACTION_TARIFF_SUBSCRIPTION_EXPIRATION_IN_3_DAYS,
                title: ActionDict.ACTION_TARIFF_SUBSCRIPTION_EXPIRATION_IN_3_DAYS,
                schema: UpcomingSubscriptionEnd3DaysMeta,
            },
            {
                code: ActionEnum.ACTION_TARIFF_SUBSCRIPTION_EXPIRATION_IN_1_DAY,
                title: ActionDict.ACTION_TARIFF_SUBSCRIPTION_EXPIRATION_IN_1_DAY,
                schema: UpcomingSubscriptionEnd1DayMeta,
            },
            {
                code: ActionEnum.ACTION_TARIFF_SUBSCRIPTION_RECURRENT_PAYMENT_FAILURE,
                title: ActionDict.ACTION_TARIFF_SUBSCRIPTION_RECURRENT_PAYMENT_FAILURE,
                schema: RecurrentPaymentFailureMeta,
            },
        ],
    },
    {
        code: NotificationGroupEnum.REGISTRATION,
        title: SettingsDict.REGISTRATION,
        users: [UserRoleEnum.system],
        actions: [
            {
                code: ActionEnum.ACTION_COURSE_ACCESS_STUDENT_INVITE_AND_REGISTER,
                title: ActionDict.ACTION_COURSE_ACCESS_STUDENT_INVITE_AND_REGISTER,
                schema: CourseStudentInviteAndRegisterMeta,
            },
        ],
    },
];
