import { Global, Module } from '@nestjs/common';

import { CoreModule } from './core/core.module';
import { NotificationsModule } from './notifications/notifications.module';
import { NotificationClientsLangScalar } from './notifications/presentation/scalars/client.scalar';

@Global()
@Module({
    imports: [CoreModule, NotificationsModule],
    providers: [NotificationClientsLangScalar],
})
export class AppModule {}
