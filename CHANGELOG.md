# v2.1.7
#### 15.04.2025

- Заменил удаление по крону старых уведомлений созданием ttl индексов

# v2.1.6
#### 11.04.2025

- Оптимизировал запрос на получение пользовательских уведомлений

# v2.1.5
#### 10.04.2025

- Убрал тестовое логирование

# v2.1.4
#### 02.04.2025

- schoolUuid в EmailServiceSendEmailToManyContract

# v2.1.3
#### 02.04.2025

- Исправил импорты в DI контейнер
- Исправил ссылку для перехода в уведомлениях
- Исправил переводы нескольких уведомлений
- Исправил ошибку `"InstanceWrapper" has thrown an unhandled exception.` связанную с запросом уведомлений неизвестным
  пользователем
- Обновил либы

# v2.1.2
#### 27.02.2025

- CI/CD

# v2.1.1
#### 27.02.2025

- CI/CD

# v2.1.0
#### 21.02.2025

- добавил переводы на шесть языков
- обновил amqp модуль

# v2.0.5
#### 18.02.2025

- убрал ограничения на длину сообщения
- добавил индексы
- переписал запрос пользовательских уведомлений

# v2.0.4
#### 18.02.2025

- фикс странного сообщения

# v2.0.3
#### 18.02.2025

- фикс удаления старой версии настроек

# v2.0.2
#### 18.02.2025

- фикс обновления старой версии настроек

# v2.0.1
#### 18.02.2025

- CI/CD

# v2.0.0
#### 14.02.2025

- Релиз обновленных уведомлений

# v1.1.24
#### 12.02.2025

- pnpm

# v1.1.23
#### 27.12.2024

1. Удалил скрипты и консюмер для синхронизации

# v1.1.22
#### 27.12.2024

1. Фикс резолвера уведомлений

# v1.1.21
#### 27.12.2024

1. Переписал скрипты для обновления userUuid, включил резолвер уведомлений

# v1.1.20
#### 27.12.2024

1. Отключил резолвер уведомлений

# v1.1.19
#### 27.12.2024

1. Убрал синхронизацию userUuid в настройках при создании уведомления

# v1.1.18
#### 26.12.2024

1. Переписал скрипты для обновления userUuid

# v1.1.17
#### 26.12.2024

1. Переписал скрипты для обновления userUuid

# v1.1.16
#### 26.12.2024

1. Убрал миграции
2. Сделал скрипты для обновления userUuid

# v1.1.15
#### 13.12.2024

1. Новые гварды из библиотеки

# v1.1.12
#### 13.12.2024

1. Добавлены

- консюмер для синхронизации schooUuid
- консюмер для синхронизации userUuid в notification_settings
- скрипт запроса синхронизации userUuid

# v1.1.11
#### 13.12.2024

1. Добавлены exchanges

# v1.1.10
#### 12.12.2024

1. CI/CD

# v1.1.9
#### 11.12.2024

1. Переписан запрос за уведомлениями на агрегацию
2. Исправление DEV развертывания
3. Check-app пайпалйн в CI/CD

# v1.1.8
#### 26.11.2024

1. Добавил индексы на уведомления пользователя, сгруппировал логи ошибок при создании пользователей
2. Установил prefetchCount = 100
3. Добавил фильтрацию отправки писем локальным пользователям

# v1.1.7
#### 25.11.2024

1. Добавил информации в лог [репозитории](src/user/user.repository.ts)

# v1.1.6
#### 25.11.2024

1. Для определения причины ошибки сделал вставку пользователей по одному [репозитории](src/user/user.repository.ts)

# v1.1.5
#### 25.11.2024

1. Убрал использование транзакции в [репозитории](src/user/user.repository.ts)

# v1.1.4
#### 25.11.2024

1. Исправил фильтрацию уникальных пользователей в [репозитории](src/user/user.repository.ts)

# v1.1.3
#### 21.11.2024

1. CI/CD

# v1.1.2
#### 21.11.2024

1. CI/CD

# v1.1.1
#### 21.11.2024

1. CI/CD

# v1.1.0
#### 15.11.2024

1. Изменена структура базы данных
2. Подключен сервис email рассылки

# v1.0.8
#### 09.10.2024

1. Обновил `@skillspace/lib` до версии 1.0.0
2. Обновил `@skillspace/amqp-contracts` до версии 1.2.2

# v1.0.7
#### 25.08.24

1. Обновил индексы

# v1.0.6
#### 25.08.24

1. Обновил индексы

# v1.0.5
#### 25.08.24

1. Обновил индексы

# v1.0.4
#### 25.08.24

1. Обновил индексы

# v1.0.3
#### 25.08.24

1. Обновил индексы

# v1.0.2
#### 25.08.24

1. Сделан db push на докерфайлах, чтобы синхронизировать индексы на проде
2. Убраны методы в репе и в резолвере, из-за которых пользователи могли удалить свои уведомления

# v1.0.8
#### 02.10.24

1. Подключил трассировки и логи Opentelemetry
