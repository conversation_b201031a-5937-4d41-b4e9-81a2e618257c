generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl", "linux-musl-arm64-openssl-1.1.x", "linux-musl-openssl-3.0.x", "linux-musl-arm64-openssl-3.0.x"]
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

enum LocaleEnum {
  ru
  en
  es
  kk
  uz
  de
  fr
  it
}

enum ActionEnum {
  ACTION_HOMEWORK_STUDENT_SENT
  ACTION_HOMEWORKS_STUDENTS_SENT_CRON
  ACTION_HOMEWORK_TEACHER_SENT
  ACTION_HOMEWORKS_TEACHER_SENT_CRON
  ACTION_BILLING_STUDENT_PAID_COURSE
  ACTION_BILLING_WITHDRAW_ACCEPTED
  ACTION_BILLING_WITHDRAW_DECLINE
  ACTION_BILLING_SCHOOL_REQUISITES_ACCEPTED
  ACTION_BILLING_SCHOOL_REQUISITES_DECLINED
  ACTION_TARIFF_SUBSCRIPTION_EXPIRED
  ACTION_TARIFF_SUBSCRIPTION_EXPIRATION_IN_7_DAYS
  ACTION_TARIFF_SUBSCRIPTION_EXPIRATION_IN_3_DAYS
  ACTION_TARIFF_SUBSCRIPTION_EXPIRATION_IN_1_DAY
  ACTION_TARIFF_SUBSCRIPTION_RECURRENT_PAYMENT_FAILURE
  ACTION_SCHOOL_SETTINGS_DISCOUNT_EXPIRED
  ACTION_COURSE_ACCESS_GROUP_STUDENT_LIMIT_OVER
  ACTION_COURSE_STUDENT_FINISH_COURSE
  ACTION_COURSE_UPCOMING_WEBINAR_STUDENT
  ACTION_COURSE_UPCOMING_WEBINAR_TEACHER
  ACTION_COURSE_WEBINAR_START_STUDENT
  ACTION_COURSE_ACCESS_STUDENT_INVITE
  ACTION_COURSE_ACCESS_STUDENT_EXPEL
  ACTION_COURSE_PUBLISHED_NEW_LESSON
  ACTION_CHAT_STUDENT_WAIT_ANSWER_24_HOUR
  ACTION_AFFILIATE_REQUISITES_ACCEPTED
  ACTION_AFFILIATE_REQUISITES_DECLINED
  ACTION_AFFILIATE_WITHDRAW_ACCEPTED
  ACTION_AFFILIATE_WITHDRAW_DECLINED
  ACTION_AFFILIATE_LEVEL_UPDATE
  ACTION_AFFILIATE_REFERRAL_PAYMENT
  ACTION_COURSE_ACCESS_STUDENT_ACCESS_END
  ACTION_COURSE_ACCESS_STUDENT_ACCESS_END_3_DAYS
  ACTION_COURSE_ACCESS_STUDENT_NEW_LESSON
  ACTION_COURSE_ACCESS_STUDENT_NEW_STEP
  ACTION_DEADLINE_UPCOMING_3_DAYS
  ACTION_DEADLINE_UPCOMING_1_DAY
  ACTION_DEADLINE_EXPIRED
  ACTION_TARIFF_FEATURE_LIMIT_EXCEEDED
}

enum NotificationSoundEnum {
  VOOP
  WOOSH
  UWA
  TRUM
  TODO
  BLOOP
}

enum ClientEnum {
  PLATFORM
  BROWSER
  MOBILE
  TELEGRAM
  EMAIL
}

enum NotificationGroupEnum {
  COURSES
  COURSE_STUDENT_FINISHED
  GROUP_STUDENT_LIMIT_OVER
  SCHOOL_SETTINGS
  DEADLINES
  CHAT
  BILLING
  HOMEWORKS
  WEBINARS
  AFFILIATE_UPDATES
  WITHDRAWAL
  TARIFFS
  SUBSCRIPTION
  REGISTRATION
}

model Notification {
  id String @id @default(auto()) @map("_id") @db.ObjectId
  version    Int @default(2)

  schoolUuid  String?
  group       NotificationGroupEnum? // После перехода на V2 сделать обязательным
  action      ActionEnum
  meta        Json

  // Relations
  userNotifications UserNotification[]
  
  createdAt DateTime  @default(now())

  @@map("notifications")
}

model UserNotification {
  id String @id @default(auto()) @map("_id") @db.ObjectId
  version    Int @default(2)

  // Дублируем для более быстрой агрегации
  schoolUuid String?
  group      NotificationGroupEnum? // После перехода на V2 сделать обязательным

  userUuid   String
  // clients    ClientEnum[] // Использовать после перехода на V2 сделать
  clients    String[]
  viewed     Boolean   @default(false)

  // Relations
  notificationId String       @db.ObjectId
  notification   Notification @relation(fields: [notificationId], references: [id])

  createdAt    DateTime  @default(now())
  updatedAt    DateTime?  @default(now())

  @@index([userUuid, clients, schoolUuid, viewed, createdAt(sort: Desc)])
  @@map("user_notifications")
}

model NotificationsSettings {
  id String @id @default(auto()) @map("_id") @db.ObjectId
  version      Int?
 
  userUuid     String  @unique
  sound    NotificationSoundEnum?

  // V1
  platform Json? // Actions[] 
  browser  Json?
  mobile   Json?
  telegram Json?
  email    Json?

  // V2
  settings Json?

  createdAt    DateTime @default(now())
  updatedAt    DateTime? @default(now())

  @@map("notifications_settings")
}

model User {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  userUuid     String  @unique
  locale       LocaleEnum?
  unionAuthKey String?

  @@index([unionAuthKey])
  @@map("users")
}
