{"compilerOptions": {"paths": {"@modules/*": ["modules/*"], "@shared/*": ["shared/*"], "@core/*": ["core/*"]}, "resolveJsonModule": true, "module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ESNext", "types": ["jest", "node"], "sourceMap": true, "outDir": "./dist", "baseUrl": "./src", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false}}