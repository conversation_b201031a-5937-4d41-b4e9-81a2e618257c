module.exports = {
    /**
     * @param db {import('mongodb').Db}
     * @param client {import('mongodb').MongoClient}
     * @returns {Promise<void>}
     */
    async up(db, client) {
        const TTL_60_DAYS = 5184000;
        await db.collection('user_notifications').createIndex({ createdAt: 1 }, { expireAfterSeconds: TTL_60_DAYS });
        await db.collection('notifications').createIndex({ createdAt: 1 }, { expireAfterSeconds: TTL_60_DAYS });
    },

    /**
     * @param db {import('mongodb').Db}
     * @param client {import('mongodb').MongoClient}
     * @returns {Promise<void>}
     */
    async down(db, client) {
        await db.collection('user_notifications').dropIndex({ createdAt: 1 });
        await db.collection('notifications').dropIndex({ createdAt: 1 });
    },
};
