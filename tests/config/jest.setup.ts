import { join } from 'node:path';
import { LogLevel } from '@skillspace/logger';
import { RabbitMQContainer } from '@testcontainers/rabbitmq';
import { config } from 'dotenv';
import { MongoMemoryReplSet } from 'mongodb-memory-server';

config({ path: join(process.cwd(), '.env.example') });

module.exports = async () => {
    const replSet = await MongoMemoryReplSet.create({ replSet: { count: 1 } });
    await replSet.waitUntilRunning();

    const uri: string = replSet.getUri('notifications');

    const rabbitMqContainer = await new RabbitMQContainer('rabbitmq:3.9-management').withReuse().start();

    process.env.RMQ_URL = rabbitMqContainer.getAmqpUrl();
    process.env.DATABASE_URL = uri;
    process.env.OTEL_DISABLED = 'true';
    process.env.LOG_LEVEL = LogLevel.Silent;

    global.__MONGO_SERVER__ = replSet;
    global.__RABBITMQ_CONTAINER__ = rabbitMqContainer;

    console.log('RabbitMQ container started with URL:', process.env.RMQ_URL);
};
