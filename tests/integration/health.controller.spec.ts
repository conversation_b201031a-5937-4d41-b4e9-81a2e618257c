import { DiagnosticsController } from '../../src/notifications/presentation/diagnostics.controller';
import { HealthController } from '../../src/notifications/presentation/health.controller';
import { app } from './test-setup';

describe('Проверка сервиса', () => {
    let healthController: HealthController;
    let diagnosticsController: DiagnosticsController;

    beforeEach(async () => {
        healthController = app.get<HealthController>(HealthController);
        diagnosticsController = app.get<DiagnosticsController>(DiagnosticsController);
    });

    it('Должны работать контроллеры диагностики', () => {
        expect(healthController).toBeDefined();
        expect(diagnosticsController).toBeDefined();
    });

    it('Должен сообщить что трейсинг отключен', async () => {
        await expect(diagnosticsController.check()).rejects.toThrow(
            expect.objectContaining({
                message: expect.stringContaining('Service Unavailable Exception'),
            }),
        );
    });

    it('Должен получить ответ о доступности сервиса', async () => {
        const result = await healthController.check();
        expect(result).toEqual({
            status: 'ok',
            info: { prisma: { status: 'up' } },
            error: {},
            details: { prisma: { status: 'up' } },
        });
    });
});
