import { z } from 'zod';

// Определение валидатора
const MetaBaseValidator = z.object({
    id: z
        .number({ message: 'ID должен быть числом' })
        .int()
        .positive({ message: 'ID должен быть положительным числом' }),
    uuid: z.string().uuid({ message: 'UUID должен быть в формате UUID v4' }),
    name: z.string({ message: 'Название должно быть строкой' }).min(1, 'Название не может быть пустым'),
});

// Вывод типа из валидатора
type MetaBaseType = z.infer<typeof MetaBaseValidator>;

// Тесты
describe.skip('Проверка работы валидатора MetaBaseValidator', () => {
    // 1. Успешная валидация
    it('Должен успешно валидировать корректные данные', () => {
        const data: MetaBaseType = {
            id: 1,
            uuid: '123e4567-e89b-12d3-a456-************', // Валидный UUID v4
            name: 'Some Name',
        };

        // Проверяем, что данные проходят валидацию
        const result = MetaBaseValidator.safeParse(data);
        expect(result.success).toBe(true);

        if (result.success) {
            expect(result.data).toEqual(data); // Проверяем, что данные совпадают
        }
    });

    // 2. Частичные данные (не все обязательные поля)
    it.only('Должен возвращать ошибку, если не переданы все обязательные поля', () => {
        const data = {
            id: '1',
            uuid: '123e4567-e89b-12d3-a456-************', // Валидный UUID v4
            // name: '',
            // name отсутствует
        };

        const result = MetaBaseValidator.safeParse(data);
        expect(result.success).toBe(false);
        console.log(formatValidationErrors(result.error));

        if (!result.success) {
            // Извлекаем ошибки из result.error
            const errors = result.error.issues;
            expect(errors).toHaveLength(3); // Ожидаем три ошибки

            // Проверяем сообщения об ошибках
            expect(errors.some((err) => err.message === 'ID должен быть положительным числом')).toBe(true);
            expect(errors.some((err) => err.message === 'UUID должен быть в формате UUID v4')).toBe(true);
            expect(errors.some((err) => err.message === 'Название не может быть пустым')).toBe(true);
        }
    });

    // 3. Некорректные данные
    it('Должен возвращать ошибку для некорректных данных', () => {
        const data = {
            id: -1, // Отрицательный ID
            uuid: 'invalid-uuid', // Невалидный UUID
            name: '', // Пустое имя
        };

        // Проверяем, что данные не проходят валидацию
        const result = MetaBaseValidator.safeParse(data);
        expect(result.success).toBe(false);

        if (!result.success) {
            expect(result.error.errors).toHaveLength(3); // Ожидаем три ошибки

            // Проверяем сообщения об ошибках
            expect(result.error.errors.some((err) => err.message === 'ID должен быть положительным числом')).toBe(true);
            expect(result.error.errors.some((err) => err.message === 'UUID должен быть в формате UUID v4')).toBe(true);
            expect(result.error.errors.some((err) => err.message === 'Название не может быть пустым')).toBe(true);
        }
    });

    // 4. Проверка с использованием .parse() (выброс исключения)
    it('Должен выбрасывать исключение при использовании .parse() с некорректными данными', () => {
        const invalidData = {
            id: -1, // Отрицательный ID
            uuid: 'invalid-uuid', // Невалидный UUID
            name: '', // Пустое имя
        };

        // Проверяем, что .parse() выбрасывает исключение
        expect(() => MetaBaseValidator.parse(invalidData)).toThrow(z.ZodError);
    });
});

function formatValidationErrors(error: z.ZodError): string[] {
    return error.issues.map((issue) => {
        const path = issue.path.join('.'); // Объединяем путь в строку
        const message = issue.message; // Сообщение об ошибке
        return `${path ? `${path}: ` : ''}${message}`;
    });
}
