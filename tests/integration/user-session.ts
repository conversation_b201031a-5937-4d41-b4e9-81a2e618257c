import { INestApplication } from '@nestjs/common';
import { LocaleEnum, NotificationGroupEnum } from '@prisma/client';
import { AuthService } from '@skillspace/access';
import { DocumentNode, print } from 'graphql';
import gql from 'graphql-tag';
import { Server } from 'http';
import * as request from 'supertest';
import TestAgent from 'supertest/lib/agent';

import { FindAllUserNotificationsDto } from '../../src/notifications/presentation/inputs/find-all-user-notifications.dto';
import { CountRow } from '../../src/notifications/presentation/inputs/get-count.dto';
import { SetSettingsInput } from '../../src/notifications/presentation/inputs/set-settings.input';
import { NotificationMapOutput } from '../../src/notifications/presentation/outputs/notification-map.output';
import {
    NotificationsSettingsOutput,
    RoleEnum,
} from '../../src/notifications/presentation/outputs/notification-settings.output';
import { UserNotificationOutput } from '../../src/notifications/presentation/outputs/user-notification.output';
import { VoidResponse } from '../../src/notifications/presentation/outputs/void-response.output';
import { PaginationDto } from '../../src/shared/dto/pagination.dto';

type JSONPrimitive = string | number | boolean | null;
type JSONArray = JSONValue[];
type JSONValue = JSONPrimitive | JSONObject | JSONArray;

export interface JSONObject {
    [member: string]: JSONValue;
}

export interface UserContext {
    actions: string[];
    role: 'ROLE_EMPLOYEE' | 'ROLE_STUDENT' | 'ROLE_GUEST';
    schoolId: string | undefined;
    userId: string | undefined;
    userName: string | undefined;
    userEmail: string | undefined;
    unionAuthKey: string | undefined;
}

const MetaBase = `
    id
    uuid
    avatar
    name
`;

const Meta = `
    whiteLabel {
        primaryColor
        hideMobileAppLinks
        isWhiteLabel
        schoolLogoUrl
        textOnly
    }
    course {
        ${MetaBase}
    }
    step {
        ${MetaBase}
    }
    student {
        ${MetaBase}
    }
    teacher {
        ${MetaBase}
    }
    lesson {
        ${MetaBase}
    }
    school {
        ${MetaBase}
    }
    group {
        id
        uuid
        avatar
        name
    }
    transaction {
        id
        createAt
        amount
    }
    promoCode {
        id
        code
        uuid
        activeFrom
        activeTo
    }
    webinar {
        startAt
    }
    requisites {
        createAt
    }
    partner {
        id
        uuid
        level {
            uuid
            slug
            name
        }
        totalEarned
        withdraw
    }
    referralPayment {
        paymentDate
        sum
        commission
        commissionSum
        school {
            id
            name
            uuid
            avatar
            url
            tariffName
        }
    }
    homeworkUser {
        id
        uuid
    }
    url
    domain
`;

export class GqlTestSession {
    private agent: TestAgent;

    constructor(
        private readonly server: Server,
        private readonly token: string,
    ) {
        this.agent = request(this.server);
    }

    private async gql<T>(
        query: DocumentNode,
        variables: Record<string, any> = {},
        locale: LocaleEnum = LocaleEnum.ru,
    ): Promise<T> {
        const queryString = print(query);

        const response = await this.agent
            .post('/graphql')
            .set('Authorization', `Bearer ${this.token}`)
            .set('Content-Type', 'application/json')
            .set('Locale', locale)
            .send({ query: queryString, variables });

        if (response.body.errors) {
            throw new Error(`GraphQL Error: ${JSON.stringify(response.body.errors)}`);
        }

        return response.body.data as T;
    }

    public async getNotificationMap(
        locale: LocaleEnum = LocaleEnum.ru,
    ): Promise<{ notificationsMap: NotificationMapOutput } | null> {
        const query = gql`
            query {
                notificationsMap {
                    actions
                    clients
                    settings
                    labels
                }
            }
        `;
        return this.gql<{ notificationsMap: NotificationMapOutput }>(query, {}, locale);
    }

    public async getNotificationSettings(
        role: RoleEnum | JSONValue,
    ): Promise<{ notificationsSettings: NotificationsSettingsOutput }> {
        const query = gql`
            query getNotificationsSettings($role: RoleEnum!) {
                notificationsSettings(role: $role) {
                    sound
                    ${NotificationGroupEnum.COURSES}
                    ${NotificationGroupEnum.GROUP_STUDENT_LIMIT_OVER}
                    ${NotificationGroupEnum.COURSE_STUDENT_FINISHED}
                    ${NotificationGroupEnum.WEBINARS}
                    ${NotificationGroupEnum.HOMEWORKS}
                    ${NotificationGroupEnum.DEADLINES}
                    ${NotificationGroupEnum.AFFILIATE_UPDATES}
                    ${NotificationGroupEnum.SCHOOL_SETTINGS}
                    ${NotificationGroupEnum.CHAT}
                    ${NotificationGroupEnum.BILLING}
                }
            }
        `;
        return this.gql<{ notificationsSettings: NotificationsSettingsOutput }>(query, { role });
    }

    public async setUserSettings(
        input: SetSettingsInput | JSONObject,
        role: RoleEnum | JSONValue,
    ): Promise<{ setNotificationsSettings: NotificationsSettingsOutput }> {
        const mutation = gql`
                mutation ($dto: SetSettingsInput!, $role: RoleEnum!) {
                setNotificationsSettings(dto: $dto, role: $role) {
                    sound
                    ${NotificationGroupEnum.COURSES}
                    ${NotificationGroupEnum.GROUP_STUDENT_LIMIT_OVER}
                    ${NotificationGroupEnum.COURSE_STUDENT_FINISHED}
                    ${NotificationGroupEnum.WEBINARS}
                    ${NotificationGroupEnum.HOMEWORKS}
                    ${NotificationGroupEnum.DEADLINES}
                    ${NotificationGroupEnum.AFFILIATE_UPDATES}
                    ${NotificationGroupEnum.SCHOOL_SETTINGS}
                    ${NotificationGroupEnum.CHAT}
                    ${NotificationGroupEnum.BILLING}
                }
            }
        `;
        return this.gql<{ setNotificationsSettings: NotificationsSettingsOutput }>(mutation, { dto: input, role });
    }

    public async getUserNotifications(
        dto: FindAllUserNotificationsDto,
        pagination: PaginationDto,
        locale: LocaleEnum = LocaleEnum.ru,
    ): Promise<{ notifications: UserNotificationOutput[] }> {
        const query = gql`
            query GetAllNotifications($pagination: PaginationDto!) {
                notifications(pagination: $pagination) {
                    id
                    createdAt
                    action
                    group
                    viewed
                    text
                    meta {
                        ${Meta}
                    }
                }
            }
        `;
        return this.gql<{ notifications: UserNotificationOutput[] }>(query, { dto, pagination }, locale);
    }

    public async getUserNotificationsCount(): Promise<{ notificationsCount: CountRow[] }> {
        const query = gql`
            query GetNotificationsCount {
                notificationsCount {
                    group
                    countAll
                    countUnread
                }
            }
        `;
        return this.gql<{ notificationsCount: CountRow[] }>(query);
    }

    public async readAll(): Promise<{ readAll: boolean }> {
        const mutation = gql`
            mutation {
                readAll
            }
        `;
        return this.gql<{ readAll: boolean }>(mutation);
    }

    public async markAsViewed(id: string): Promise<{ markAsViewed: VoidResponse }> {
        const mutation = gql`
            mutation MarkAsViewed($id: ID!) {
                markAsViewed(id: $id) {
                    success
                }
            }
        `;
        return this.gql<{ markAsViewed: VoidResponse }>(mutation, { id });
    }

    public async markUnread(id: string): Promise<{ markUnread: VoidResponse }> {
        const mutation = gql`
            mutation MarkAUnread($id: ID!) {
                markUnread(id: $id) {
                    success
                }
            }
        `;
        return this.gql<{ markUnread: VoidResponse }>(mutation, { id });
    }
}

export async function createGqlTestSession(app: INestApplication, context: UserContext): Promise<GqlTestSession> {
    const authService = app.get<AuthService>(AuthService);
    const token = await authService.signAuthorizationToken(context);

    // const userToken = await authService.signAuthorizationToken({
    //     userId: '4a22c9e5-67f5-44fb-aabc-afb61cf592b6',
    //     schoolId: '3479a48b-bd9b-4d79-be83-2960d0dbe7ec',
    //     role: 'ROLE_EMPLOYEE' as const,
    //     actions: [],
    //     userName: 'Спиридон Никанорович',
    //     userEmail: '<EMAIL>',
    //     unionAuthKey: '24',
    // });
    // console.log({ userToken });
    const server: Server = app.getHttpServer();
    return new GqlTestSession(server, token);
}
