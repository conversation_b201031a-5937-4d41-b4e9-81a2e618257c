import { randomUUID } from 'node:crypto';
import { NotificationsCreateNotificationV2ContractNamespace } from '@skillspace/amqp-contracts';

import * as users from './notify/__data__/users.message.json';
import { META } from './notify/notification-meta-constants';
import { TestAction } from './settings-constants';
import { SCHOOL } from './user-constants';

export function getUsers(userIds: number[] = [1, 2, 3, 4, 5]) {
    return userIds.map((id) => users[id - 1]);
}

const AFFILIATE_ACTIONS = [
    TestAction.ACTION_AFFILIATE_REQUISITES_ACCEPTED,
    TestAction.ACTION_AFFILIATE_REQUISITES_DECLINED,
    TestAction.ACTION_AFFILIATE_WITHDRAW_ACCEPTED,
    TestAction.ACTION_AFFILIATE_WITHDRAW_DECLINED,
    TestAction.ACTION_AFFILIATE_LEVEL_UPDATE,
    TestAction.ACTION_AFFILIATE_REFERRAL_PAYMENT,
];

export function createMessageV2(
    action: NotificationsCreateNotificationV2ContractNamespace.NotificationActionsEnum,
    users,
) {
    const isAffiliateAction = AFFILIATE_ACTIONS.includes(action);

    const payload = {
        users,
        text: META[action].text,
        action,
        meta: META[action].meta,
    };
    return {
        timestamp: Date.now(),
        requestUuid: randomUUID(),
        payload: isAffiliateAction
            ? payload
            : {
                  ...payload,
                  school: SCHOOL,
              },
    };
}

export function getAllTypeMessages() {
    const actions = Object.keys(META); // 38

    // const testActions = Object.keys(TestAction);
    // console.log({
    //     actionsCount: actions.length,
    //     testActionsCount: testActions.length,
    // });

    // const uniqueTestAction = testActions.find((action) => !actions.includes(action));
    // console.log('Элемент, который есть в testActions, но нет в actions:', uniqueTestAction);

    return actions.map((action) =>
        createMessageV2(
            action as NotificationsCreateNotificationV2ContractNamespace.NotificationActionsEnum,
            getUsers(),
        ),
    );
}
