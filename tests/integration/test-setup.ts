import 'reflect-metadata';

import { INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { PrismaClient } from '@prisma/client';
import { createLogger } from '@skillspace/logger';

// import { Db, MongoClient } from 'mongodb';
import { AppModule } from '../../src/app.module';
import { setupGlobalMiddlewares } from '../../src/bootstrap-setup';
import { IBrowserPublisher } from '../../src/notifications/domain/infrastructure/publishers/browser-publisher.interface';
import { IEmailPublisher } from '../../src/notifications/domain/infrastructure/publishers/email-publisher.interface';
import { ITelegramPublisher } from '../../src/notifications/domain/infrastructure/publishers/telegram-publisher.interface';
import { BROWSER_PUBLISHER, EMAIL_PUBLISHER, TELEGRAM_PUBLISHER } from '../../src/notifications/injects';
import { NotificationsConsumer } from '../../src/notifications/presentation/notifications.consumer';
import { SettingsResolver } from '../../src/notifications/presentation/settings.resolver';
import { UserNotificationsResolver } from '../../src/notifications/presentation/user-notifications.resolver';
import { USER_01, USER_02 } from './user-constants';
import { createGqlTestSession, GqlTestSession } from './user-session';

let app: INestApplication;
let prisma: PrismaClient;
// let mongoClient: MongoClient;
// let db: Db;

let settingsResolver: SettingsResolver;
let notificationsResolver: UserNotificationsResolver;
let notificationConsumer: NotificationsConsumer;

let emailPublisher: IEmailPublisher;
let telegramPublisher: ITelegramPublisher;
let wsPublisher: IBrowserPublisher;

let emailSpy: jest.SpyInstance;
let telegramSpy: jest.SpyInstance;
let wsSpy: jest.SpyInstance;

let userSession1: GqlTestSession;
let userSession2: GqlTestSession;

beforeAll(async () => {
    const builder = Test.createTestingModule({
        imports: [AppModule],
    });

    const moduleFixture = await builder.compile();
    app = moduleFixture.createNestApplication({ logger: await createLogger() });

    setupGlobalMiddlewares(app);

    await app.init();

    settingsResolver = app.get<SettingsResolver>(SettingsResolver);
    notificationsResolver = app.get<UserNotificationsResolver>(UserNotificationsResolver);
    notificationConsumer = app.get<NotificationsConsumer>(NotificationsConsumer);

    emailPublisher = app.get<IEmailPublisher>(EMAIL_PUBLISHER);
    telegramPublisher = app.get<ITelegramPublisher>(TELEGRAM_PUBLISHER);
    wsPublisher = app.get<IBrowserPublisher>(BROWSER_PUBLISHER);

    userSession1 = await createGqlTestSession(app, USER_01);
    userSession2 = await createGqlTestSession(app, USER_02);

    prisma = new PrismaClient();
    await prisma.$connect();

    // mongoClient = new MongoClient(process.env.DATABASE_URL);
    // await mongoClient.connect();
    // db = mongoClient.db('notifications');
}, 30000);

beforeEach(() => {
    jest.clearAllMocks();
    emailSpy = jest.spyOn(emailPublisher, 'sendMessage');
    telegramSpy = jest.spyOn(telegramPublisher, 'sendMessage');
    wsSpy = jest.spyOn(wsPublisher, 'sendMessage');
});

afterAll(async () => {
    await app.close();
    await prisma.$disconnect();
    // await mongoClient.close();
}, 20000);

export {
    app,
    // db,
    emailSpy,
    notificationConsumer,
    notificationsResolver,
    prisma,
    settingsResolver,
    telegramSpy,
    userSession1,
    userSession2,
    wsSpy,
};
