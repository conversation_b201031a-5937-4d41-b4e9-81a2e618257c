import { UserContext } from './user-session';

export const SCHOOL_UUID = '6fc112f6-e471-4d83-94c2-ff447f43d5e3';

export const SCHOOL = {
    id: 1,
    uuid: SCHOOL_UUID,
    email: '<EMAIL>',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    slug: 'school',
};

export const USER_01_UUID = '6fc112f6-e471-4d83-94c2-ff447f43d5e1';
export const USER_02_UUID = '6fc112f6-e471-4d83-94c2-ff447f43d5e2';
export const USER_03_UUID = '6fc112f6-e471-4d83-94c2-ff447f43d5e3';
export const USER_04_UUID = '6fc112f6-e471-4d83-94c2-ff447f43d5e4';

export const USER_01: UserContext = {
    userId: USER_01_UUID,
    role: 'ROLE_STUDENT' as const,
    actions: [],
    schoolId: SCHOOL_UUID,
    userName: 'Иван Васильевич',
    userEmail: '<EMAIL>',
    unionAuthKey: '23',
};

export const USER_02: UserContext = {
    userId: USER_02_UUID,
    role: 'ROLE_EMPLOYEE' as const,
    actions: [],
    schoolId: SCHOOL_UUID,
    userName: 'Спиридон Никанорович',
    userEmail: '<EMAIL>',
    unionAuthKey: '24',
};
