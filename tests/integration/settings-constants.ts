import { NotificationsCreateNotificationV2ContractNamespace } from '@skillspace/amqp-contracts';

export const DATE_NOW = new Date('2020-01-01T13:00:00.000Z').getTime();
export const TWO_HOURS = 2 * 60 * 60 * 1000;
export const FAKE_UUID = '6fc112f6-e471-4d83-94c2-ff447f43d5e0';

export const TestAction = NotificationsCreateNotificationV2ContractNamespace.NotificationActionsEnum;

export const SYSTEM_GROUPS = ['WITHDRAWAL', 'TARIFFS', 'SUBSCRIPTION'];

export const CUSTOM_GROUPS = [
    'COURSES',
    'GROUP_STUDENT_LIMIT_OVER',
    'COURSE_STUDENT_FINISHED',
    'WEBINARS',
    'HOMEWORKS',
    'DEADLINES',
    'AFFILIATE_UPDATES',
    'SCHOOL_SETTINGS',
    'CHAT',
    'BILLING',
];

export const STUDENT_GROUPS = ['COURSES', 'WEBINARS', 'HOMEWORKS', 'DEADLINES', 'AFFILIATE_UPDATES'];

export const TEACHER_GROUPS = [
    'GROUP_STUDENT_LIMIT_OVER',
    'COURSE_STUDENT_FINISHED',
    'WEBINARS',
    'HOMEWORKS',
    'AFFILIATE_UPDATES',
    'SCHOOL_SETTINGS',
    'CHAT',
    'BILLING',
];

export const CUSTOM_DEFAULT_SETTINGS = {
    sound: 'VOOP',
    COURSES: ['PLATFORM'],
    GROUP_STUDENT_LIMIT_OVER: ['PLATFORM'],
    COURSE_STUDENT_FINISHED: ['PLATFORM'],
    WEBINARS: ['PLATFORM'],
    HOMEWORKS: ['PLATFORM'],
    DEADLINES: ['PLATFORM'],
    AFFILIATE_UPDATES: ['PLATFORM'],
    SCHOOL_SETTINGS: ['PLATFORM'],
    CHAT: ['PLATFORM'],
    BILLING: ['PLATFORM'],
};

export const STUDENT_DEFAULT_SETTINGS = {
    sound: 'VOOP',
    COURSES: ['PLATFORM'],
    GROUP_STUDENT_LIMIT_OVER: null,
    COURSE_STUDENT_FINISHED: null,
    WEBINARS: ['PLATFORM'],
    HOMEWORKS: ['PLATFORM'],
    DEADLINES: ['PLATFORM'],
    AFFILIATE_UPDATES: ['PLATFORM'],
    SCHOOL_SETTINGS: null,
    CHAT: null,
    BILLING: null,
};

export const TEACHER_DEFAULT_SETTINGS = {
    sound: 'VOOP',
    COURSES: null,
    GROUP_STUDENT_LIMIT_OVER: ['PLATFORM'],
    COURSE_STUDENT_FINISHED: ['PLATFORM'],
    WEBINARS: ['PLATFORM'],
    HOMEWORKS: ['PLATFORM'],
    DEADLINES: null,
    AFFILIATE_UPDATES: ['PLATFORM'],
    SCHOOL_SETTINGS: ['PLATFORM'],
    CHAT: ['PLATFORM'],
    BILLING: ['PLATFORM'],
};
