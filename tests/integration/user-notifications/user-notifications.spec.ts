import { LocaleEnum, Prisma } from '@prisma/client';

import { GetUserNotificationsQH } from '../../../src/notifications/application/queries/get-user-notifications';
import * as EnTexts from '../__data__/notification-texts-en.response.json';
import * as RuTexts from '../__data__/notification-texts-ru.response.json';
import * as userSettingsData from '../__data__/notifications-settings.data.json';
import { TestAction } from '../settings-constants';
import { createMessageV2, getAllTypeMessages, getUsers } from '../test.utils';
import { app, notificationConsumer, notificationsResolver, prisma, userSession1 } from '../test-setup';

describe('Список уведомлений', () => {
    beforeAll(async () => {
        await prisma.notificationsSettings.createMany({
            data: userSettingsData as Prisma.NotificationsSettingsCreateInput[],
        });
    });

    afterAll(async () => {
        await prisma.notificationsSettings.deleteMany();
        await prisma.userNotification.deleteMany();
        await prisma.notification.deleteMany();
    });

    it('Резолвер уведомлений должен быть доступен', () => {
        expect(notificationsResolver).toBeDefined();
    });

    it('Обработчик запроса получения уведомлений должен быть доступен', () => {
        const queryHandler = app.get(GetUserNotificationsQH);
        expect(queryHandler).toBeDefined();
    });

    describe('Список', () => {
        beforeAll(async () => {
            const messages = getAllTypeMessages();
            await Promise.all(messages.map((message) => notificationConsumer.handleNotificationsMessageV2(message)));
        });

        afterAll(async () => {
            await prisma.userNotification.deleteMany();
            await prisma.notification.deleteMany();
        });

        it('Должен получить все уведомления за исключением уведомлений по cron', async () => {
            const list = await userSession1.getUserNotifications({ viewed: false }, { skip: 0, take: 100 });
            expect(list.notifications.length).toEqual(36);
        });

        it('Должен получить список уведомлений с генерацией текста на русском', async () => {
            const sortWebinars = (a, b) => a.action.localeCompare(b.action);
            const filterWebinars = (t) => !t.text.startsWith('До начала вебинара');

            const result = await userSession1.getUserNotifications({ viewed: false }, { skip: 0, take: 100 });
            const messageTexts = result.notifications
                .map((n) => ({ action: n.action, text: n.text }))
                .filter(filterWebinars)
                .sort(sortWebinars);

            expect(messageTexts).toEqual(RuTexts.filter(filterWebinars));
        });

        it('Должен получить список уведомлений с генерацией текста на английском', async () => {
            const sortWebinars = (a, b) => a.action.localeCompare(b.action);
            const filterWebinars = (t) => !t.text.startsWith('The webinar');

            const result = await userSession1.getUserNotifications(
                { viewed: false },
                { skip: 0, take: 100 },
                LocaleEnum.en,
            );
            const messageTexts = result.notifications
                .map((n) => ({ action: n.action, text: n.text }))
                .filter(filterWebinars)
                .sort(sortWebinars);

            expect(messageTexts).toEqual(EnTexts.filter(filterWebinars));
        });

        it('Должен получить количество уведомление', async () => {
            const count = await userSession1.getUserNotificationsCount();
            expect(count).toEqual({
                notificationsCount: [
                    { group: 'AFFILIATE_UPDATES', countAll: 6, countUnread: 6 },
                    { group: 'BILLING', countAll: 1, countUnread: 1 },
                    { group: 'CHAT', countAll: 1, countUnread: 1 },
                    { group: 'COURSE_STUDENT_FINISHED', countAll: 1, countUnread: 1 },
                    { group: 'COURSES', countAll: 7, countUnread: 7 },
                    { group: 'DEADLINES', countAll: 3, countUnread: 3 },
                    { group: 'GROUP_STUDENT_LIMIT_OVER', countAll: 1, countUnread: 1 },
                    { group: 'HOMEWORKS', countAll: 2, countUnread: 2 },
                    { group: 'SCHOOL_SETTINGS', countAll: 1, countUnread: 1 },
                    { group: 'SUBSCRIPTION', countAll: 5, countUnread: 5 },
                    { group: 'TARIFFS', countAll: 1, countUnread: 1 },
                    { group: 'WEBINARS', countAll: 3, countUnread: 3 },
                    { group: 'WITHDRAWAL', countAll: 4, countUnread: 4 },
                ],
            });
        });
    });

    describe('Отметить прочтенными', () => {
        afterEach(async () => {
            await prisma.userNotification.deleteMany();
            await prisma.notification.deleteMany();
        });

        it('Должен пометить все уведомление как прочитанные', async () => {
            const messages = getAllTypeMessages();
            await Promise.all(messages.map((message) => notificationConsumer.handleNotificationsMessageV2(message)));

            const countBefore = await userSession1.getUserNotificationsCount();
            expect(countBefore).toEqual({
                notificationsCount: [
                    { group: 'AFFILIATE_UPDATES', countAll: 6, countUnread: 6 },
                    { group: 'BILLING', countAll: 1, countUnread: 1 },
                    { group: 'CHAT', countAll: 1, countUnread: 1 },
                    { group: 'COURSE_STUDENT_FINISHED', countAll: 1, countUnread: 1 },
                    { group: 'COURSES', countAll: 7, countUnread: 7 },
                    { group: 'DEADLINES', countAll: 3, countUnread: 3 },
                    { group: 'GROUP_STUDENT_LIMIT_OVER', countAll: 1, countUnread: 1 },
                    { group: 'HOMEWORKS', countAll: 2, countUnread: 2 },
                    { group: 'SCHOOL_SETTINGS', countAll: 1, countUnread: 1 },
                    { group: 'SUBSCRIPTION', countAll: 5, countUnread: 5 },
                    { group: 'TARIFFS', countAll: 1, countUnread: 1 },
                    { group: 'WEBINARS', countAll: 3, countUnread: 3 },
                    { group: 'WITHDRAWAL', countAll: 4, countUnread: 4 },
                ],
            });

            const result = await userSession1.readAll();
            expect(result).toEqual({ readAll: 36 });

            const count = await userSession1.getUserNotificationsCount();
            expect(count).toEqual({
                notificationsCount: [
                    { group: 'AFFILIATE_UPDATES', countAll: 6, countUnread: 0 },
                    { group: 'BILLING', countAll: 1, countUnread: 0 },
                    { group: 'CHAT', countAll: 1, countUnread: 0 },
                    { group: 'COURSE_STUDENT_FINISHED', countAll: 1, countUnread: 0 },
                    { group: 'COURSES', countAll: 7, countUnread: 0 },
                    { group: 'DEADLINES', countAll: 3, countUnread: 0 },
                    { group: 'GROUP_STUDENT_LIMIT_OVER', countAll: 1, countUnread: 0 },
                    { group: 'HOMEWORKS', countAll: 2, countUnread: 0 },
                    { group: 'SCHOOL_SETTINGS', countAll: 1, countUnread: 0 },
                    { group: 'SUBSCRIPTION', countAll: 5, countUnread: 0 },
                    { group: 'TARIFFS', countAll: 1, countUnread: 0 },
                    { group: 'WEBINARS', countAll: 3, countUnread: 0 },
                    { group: 'WITHDRAWAL', countAll: 4, countUnread: 0 },
                ],
            });
        });

        it('Должен пометить уведомление как прочитанное', async () => {
            await userSession1.setUserSettings({ sound: 'TODO', COURSES: ['PLATFORM', 'EMAIL'] }, 'student');
            await notificationConsumer.handleNotificationsMessageV2(
                createMessageV2(TestAction.ACTION_COURSE_ACCESS_STUDENT_INVITE, getUsers()),
            );

            const list = await userSession1.getUserNotifications({ viewed: false }, { skip: 0, take: 10 });
            const userNotificationId = list.notifications[0].id;

            const count1 = await userSession1.getUserNotificationsCount();
            expect(count1).toEqual({
                notificationsCount: [{ group: 'COURSES', countAll: 1, countUnread: 1 }],
            });

            const readResult = await userSession1.markAsViewed(userNotificationId);
            expect(readResult).toEqual({ markAsViewed: { success: true } });

            const count2 = await userSession1.getUserNotificationsCount();
            expect(count2).toEqual({
                notificationsCount: [{ group: 'COURSES', countAll: 1, countUnread: 0 }],
            });
        });

        it('Должен пометить уведомление как непрочитанное', async () => {
            await userSession1.setUserSettings({ sound: 'TODO', COURSES: ['PLATFORM', 'EMAIL'] }, 'student');
            await notificationConsumer.handleNotificationsMessageV2(
                createMessageV2(TestAction.ACTION_COURSE_ACCESS_STUDENT_INVITE, getUsers()),
            );
            const list = await userSession1.getUserNotifications({ viewed: false }, { skip: 0, take: 10 });
            const userNotificationId = list.notifications[0].id;
            await userSession1.markAsViewed(userNotificationId);

            const countBefore = await userSession1.getUserNotificationsCount();
            expect(countBefore).toEqual({
                notificationsCount: [{ group: 'COURSES', countAll: 1, countUnread: 0 }],
            });

            const unReadResult = await userSession1.markUnread(userNotificationId);
            const countAfter = await userSession1.getUserNotificationsCount();

            expect(unReadResult).toEqual({ markUnread: { success: true } });
            expect(countAfter).toEqual({
                notificationsCount: [{ group: 'COURSES', countAll: 1, countUnread: 1 }],
            });
        });
    });
});
