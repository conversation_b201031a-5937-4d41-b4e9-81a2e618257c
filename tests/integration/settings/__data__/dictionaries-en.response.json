{"actions": {"ACTION_COURSE_PUBLISHED_NEW_LESSON": "New lessons added to the course", "ACTION_COURSE_ACCESS_STUDENT_ACCESS_END": "Course access expired", "ACTION_COURSE_ACCESS_STUDENT_ACCESS_END_3_DAYS": "Course access will expire in 3 days", "ACTION_COURSE_ACCESS_STUDENT_NEW_LESSON": "New lesson access granted", "ACTION_COURSE_ACCESS_STUDENT_NEW_STEP": "New step access granted [module]", "ACTION_COURSE_ACCESS_STUDENT_INVITE": "You've been granted access to a new course", "ACTION_COURSE_ACCESS_STUDENT_EXPEL": "Administrator revoked course access", "ACTION_COURSE_ACCESS_GROUP_STUDENT_LIMIT_OVER": "Group is full", "ACTION_COURSE_STUDENT_FINISH_COURSE": "Student completed the course", "ACTION_COURSE_UPCOMING_WEBINAR_STUDENT": "Upcoming webinar reminder", "ACTION_COURSE_WEBINAR_START_STUDENT": "Live stream has started", "ACTION_COURSE_UPCOMING_WEBINAR_TEACHER": "Upcoming webinar reminder", "ACTION_HOMEWORK_STUDENT_SENT": "Student submitted homework assignment", "ACTION_HOMEWORK_TEACHER_SENT": "Teacher sent feedback", "ACTION_HOMEWORKS_STUDENTS_SENT_CRON": "Students submitted homework assignments", "ACTION_HOMEWORKS_TEACHER_SENT_CRON": "Your homework has been checked", "ACTION_DEADLINE_UPCOMING_1_DAY": "Approaching deadline (1 day left)", "ACTION_DEADLINE_UPCOMING_3_DAYS": "Approaching deadline (3 days left)", "ACTION_DEADLINE_EXPIRED": "Deadline expired", "ACTION_AFFILIATE_REQUISITES_ACCEPTED": "Partner requisites approved", "ACTION_AFFILIATE_REQUISITES_DECLINED": "Partner requisites declined", "ACTION_AFFILIATE_WITHDRAW_ACCEPTED": "Affiliate withdrawal approved", "ACTION_AFFILIATE_WITHDRAW_DECLINED": "Affiliate withdrawal declined", "ACTION_AFFILIATE_LEVEL_UPDATE": "Partner status updated", "ACTION_AFFILIATE_REFERRAL_PAYMENT": "New referral payment received", "ACTION_SCHOOL_SETTINGS_DISCOUNT_EXPIRED": "Promo code expired", "ACTION_CHAT_STUDENT_WAIT_ANSWER_24_HOUR": "Student has been waiting for your response for 24 hours", "ACTION_BILLING_STUDENT_PAID_COURSE": "Student paid for the course", "ACTION_BILLING_WITHDRAW_ACCEPTED": "<PERSON><PERSON><PERSON> request approved", "ACTION_BILLING_WITHDRAW_DECLINE": "<PERSON>drawal request declined", "ACTION_BILLING_SCHOOL_REQUISITES_ACCEPTED": "Verification passed", "ACTION_BILLING_SCHOOL_REQUISITES_DECLINED": "Verification failed", "ACTION_TARIFF_FEATURE_LIMIT_EXCEEDED": "School feature limit exceeded", "ACTION_TARIFF_SUBSCRIPTION_EXPIRED": "Subscription expired", "ACTION_TARIFF_SUBSCRIPTION_EXPIRATION_IN_7_DAYS": "7 days left until subscription expiration", "ACTION_TARIFF_SUBSCRIPTION_EXPIRATION_IN_3_DAYS": "3 days left until subscription expiration", "ACTION_TARIFF_SUBSCRIPTION_EXPIRATION_IN_1_DAY": "Subscription ends today (students will lose access to courses)", "ACTION_TARIFF_SUBSCRIPTION_RECURRENT_PAYMENT_FAILURE": "Autopayment failed"}, "clients": {"PLATFORM": "Platform", "BROWSER": "Browser", "EMAIL": "Email", "TELEGRAM": "Telegram", "MOBILE": "Mobile"}, "settings": {"COURSES": "Course updates and access", "GROUP_STUDENT_LIMIT_OVER": "No more spots in the group", "COURSE_STUDENT_FINISHED": "Student completed the course", "WEBINARS": "Reminder about the upcoming webinar", "HOMEWORKS": "Homework notifications", "DEADLINES": "Deadline reminders", "AFFILIATE_UPDATES": "Affiliate updates and transactions", "SCHOOL_SETTINGS": "Promo code expired", "CHAT": "Student waiting for your reply (24h)", "BILLING": "Student paid for the course", "WITHDRAWAL": "<PERSON><PERSON><PERSON>", "TARIFFS": "Tariff", "SUBSCRIPTION": "Subscription"}, "labels": {"AFFILIATE_UPDATES": "Affiliates", "BILLING": "Payments", "CHAT": "Cha<PERSON>", "COURSES": "Courses", "COURSE_STUDENT_FINISHED": "Achievements", "DEADLINES": "Deadlines", "GROUP_STUDENT_LIMIT_OVER": "Limits", "HOMEWORKS": "Homeworks", "SCHOOL_SETTINGS": "Promo", "SUBSCRIPTION": "Subscription", "TARIFFS": "Tariff", "WEBINARS": "Webinars", "WITHDRAWAL": "<PERSON><PERSON><PERSON>"}}