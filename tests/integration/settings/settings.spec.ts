import { LocaleEnum, Prisma } from '@prisma/client';

import { NotificationSchemaService } from '../../../src/notifications/application/services/notification-schema.service';
import * as userSettingsData from '../__data__/notifications-settings.data.json';
import {
    CUSTOM_GROUPS,
    STUDENT_DEFAULT_SETTINGS,
    STUDENT_GROUPS,
    SYSTEM_GROUPS,
    TEACHER_DEFAULT_SETTINGS,
    TEACHER_GROUPS,
} from '../settings-constants';
import { app, prisma, settingsResolver, userSession1, userSession2 } from '../test-setup';
import { USER_01 } from '../user-constants';
import { createGqlTestSession, GqlTestSession } from '../user-session';
import * as DICTIONARIES_RESPONSE_EN from './__data__/dictionaries-en.response.json';
import * as DICTIONARIES_RESPONSE_RU from './__data__/dictionaries-ru.response.json';
import * as oldSettings from './__data__/old-settings.data.json';

const ALL_PLATFORMS = ['PLATFORM', 'MOBILE', 'EMAIL', 'BROWSER', 'TELEGRAM'];

describe('Настройки пользователя', () => {
    let schema: NotificationSchemaService;

    beforeAll(async () => {
        schema = app.get<NotificationSchemaService>(NotificationSchemaService);
    });

    afterEach(async () => {
        await prisma.notificationsSettings.deleteMany();
    });

    it('Резолвер должен быть доступен', () => {
        expect(settingsResolver).toBeDefined();
    });

    describe('Схема', () => {
        it('Должен получить коды настроек студента', async () => {
            expect(schema.getStudentGroupCodes()).toEqual(STUDENT_GROUPS);
        });

        it('Должен получить коды настроек преподавателя', async () => {
            expect(schema.getTeacherGroupCodes()).toEqual(TEACHER_GROUPS);
        });

        it('Должен получить коды системных настроек', async () => {
            expect(schema.getSystemGroupCodes()).toEqual(SYSTEM_GROUPS);
        });

        it('Должен получить коды пользовательских настроек', async () => {
            expect(schema.getCustomGroupCodes()).toEqual(CUSTOM_GROUPS);
        });
    });

    describe('Получение настроек пользователя', () => {
        it('Должен получить словари настроек на русском', async () => {
            const dictionaries = await userSession1.getNotificationMap();
            expect(dictionaries).toEqual({ notificationsMap: DICTIONARIES_RESPONSE_RU });
        });

        it('Должен получить словари настроек английском', async () => {
            const dictionaries = await userSession1.getNotificationMap(LocaleEnum.en);
            expect(dictionaries).toEqual({ notificationsMap: DICTIONARIES_RESPONSE_EN });
        });

        it('Должен получить дефолтные настройки для нового студента', async () => {
            const platformSettings = await userSession1.getNotificationSettings('student');
            expect(platformSettings).toEqual({ notificationsSettings: STUDENT_DEFAULT_SETTINGS });
        });

        it('Должен получить дефолтные настройки для нового преподавателя', async () => {
            const platformSettings = await userSession1.getNotificationSettings('teacher');
            expect(platformSettings).toEqual({ notificationsSettings: TEACHER_DEFAULT_SETTINGS });
        });
    });

    describe('Установка настроек пользователя', () => {
        afterEach(async () => {
            await prisma.notificationsSettings.deleteMany();
        });

        it('Должен создать дефолтные настройки с выбранными звуковыми уведомлениями для студента', async () => {
            const input = { sound: 'TODO' };
            const settings = await userSession1.setUserSettings(input, 'student');
            expect(settings).toEqual({ setNotificationsSettings: { ...STUDENT_DEFAULT_SETTINGS, ...input } });
        });

        it('Должен создать настройки уведомлений с выбранными опциями для студента', async () => {
            const input = {
                sound: 'TRUM',
                WEBINARS: ['EMAIL'],
                HOMEWORKS: ['EMAIL'],
            };
            const settings = await userSession2.setUserSettings(input, 'student');
            expect(settings).toEqual({ setNotificationsSettings: { ...STUDENT_DEFAULT_SETTINGS, ...input } });
        });

        it('Должен получить созданные кастомные настройки для студента', async () => {
            const input = {
                sound: 'TRUM',
                WEBINARS: ['EMAIL'],
                HOMEWORKS: ['EMAIL'],
            };
            await userSession1.setUserSettings(input, 'student');

            const settings = await userSession1.getNotificationSettings('student');
            expect(settings).toEqual({ notificationsSettings: { ...STUDENT_DEFAULT_SETTINGS, ...input } });
        });

        it('Должен отключить дефолтные уведомления для студента', async () => {
            const resetInput = {
                sound: null,
                COURSES: [],
                WEBINARS: [],
                HOMEWORKS: [],
                DEADLINES: [],
                AFFILIATE_UPDATES: [],
            };
            const settings = await userSession1.setUserSettings(resetInput, 'student');
            expect(settings).toEqual({ setNotificationsSettings: { ...STUDENT_DEFAULT_SETTINGS, ...resetInput } });
        });

        it('Не должен применять настройки, если в качестве настройки группы используется null', async () => {
            const resetInput = {
                COURSES: null,
                WEBINARS: null,
            };
            const settings = await userSession1.setUserSettings(resetInput, 'student');
            expect(settings).toEqual({ setNotificationsSettings: STUDENT_DEFAULT_SETTINGS });
        });

        it('Должен отключить звук уведомлений', async () => {
            const resetInput = { sound: null };
            const settings = await userSession1.setUserSettings(resetInput, 'student');
            expect(settings.setNotificationsSettings.sound).toEqual(null);
        });
    });

    describe('Изменение настроек пользователя', () => {
        beforeEach(async () => {
            await prisma.notificationsSettings.createMany({
                data: userSettingsData as Prisma.NotificationsSettingsCreateInput[],
            });
        });

        it('Должен изменить настройки уведомлений для студента', async () => {
            const before = await userSession2.getNotificationSettings('student');
            expect(before).toEqual({
                notificationsSettings: {
                    sound: 'VOOP',
                    COURSES: ALL_PLATFORMS,
                    GROUP_STUDENT_LIMIT_OVER: null,
                    COURSE_STUDENT_FINISHED: null,
                    WEBINARS: ALL_PLATFORMS,
                    HOMEWORKS: ALL_PLATFORMS,
                    DEADLINES: ALL_PLATFORMS,
                    AFFILIATE_UPDATES: ALL_PLATFORMS,
                    SCHOOL_SETTINGS: null,
                    CHAT: null,
                    BILLING: null,
                },
            });

            const input = {
                sound: 'TRUM',
                WEBINARS: ['EMAIL'],
                HOMEWORKS: ['EMAIL'],
            };
            await userSession2.setUserSettings(input, 'student');

            const settings = await userSession2.getNotificationSettings('student');

            expect(settings).toEqual({
                notificationsSettings: {
                    sound: 'TRUM',
                    COURSES: ALL_PLATFORMS,
                    GROUP_STUDENT_LIMIT_OVER: null,
                    COURSE_STUDENT_FINISHED: null,
                    WEBINARS: ['EMAIL'],
                    HOMEWORKS: ['EMAIL'],
                    DEADLINES: ALL_PLATFORMS,
                    AFFILIATE_UPDATES: ALL_PLATFORMS,
                    SCHOOL_SETTINGS: null,
                    CHAT: null,
                    BILLING: null,
                },
            });
        });

        it('Должен изменить настройки уведомлений для преподавателя', async () => {
            const before = await userSession2.getNotificationSettings('teacher');
            expect(before).toEqual({
                notificationsSettings: {
                    sound: 'VOOP',
                    COURSES: null,
                    GROUP_STUDENT_LIMIT_OVER: ALL_PLATFORMS,
                    COURSE_STUDENT_FINISHED: ALL_PLATFORMS,
                    WEBINARS: ALL_PLATFORMS,
                    HOMEWORKS: ALL_PLATFORMS,
                    DEADLINES: null,
                    AFFILIATE_UPDATES: ALL_PLATFORMS,
                    SCHOOL_SETTINGS: ALL_PLATFORMS,
                    CHAT: ALL_PLATFORMS,
                    BILLING: ALL_PLATFORMS,
                },
            });

            const input = {
                sound: 'TRUM',
                WEBINARS: ['EMAIL'],
                HOMEWORKS: ['EMAIL'],
            };
            const settings = await userSession2.setUserSettings(input, 'teacher');

            expect(settings).toEqual({
                setNotificationsSettings: {
                    sound: 'TRUM',
                    COURSES: null,
                    GROUP_STUDENT_LIMIT_OVER: ALL_PLATFORMS,
                    COURSE_STUDENT_FINISHED: ALL_PLATFORMS,
                    WEBINARS: ['EMAIL'],
                    HOMEWORKS: ['EMAIL'],
                    DEADLINES: null,
                    AFFILIATE_UPDATES: ALL_PLATFORMS,
                    SCHOOL_SETTINGS: ALL_PLATFORMS,
                    CHAT: ALL_PLATFORMS,
                    BILLING: ALL_PLATFORMS,
                },
            });
        });

        it('Не должен затирать настройки, если передавать null', async () => {
            const input = {
                sound: 'TRUM',
                COURSES: ['PLATFORM', 'EMAIL'],
                GROUP_STUDENT_LIMIT_OVER: null,
                COURSE_STUDENT_FINISHED: null,
                WEBINARS: null,
                HOMEWORKS: null,
                DEADLINES: null,
                AFFILIATE_UPDATES: null,
                SCHOOL_SETTINGS: null,
                CHAT: null,
                BILLING: null,
            };
            await userSession2.setUserSettings(input, 'student');

            const settings = await userSession2.getNotificationSettings('student');
            expect(settings).toEqual({
                notificationsSettings: {
                    sound: 'TRUM',
                    COURSES: ['PLATFORM', 'EMAIL'],
                    GROUP_STUDENT_LIMIT_OVER: null,
                    COURSE_STUDENT_FINISHED: null,
                    WEBINARS: ALL_PLATFORMS,
                    HOMEWORKS: ALL_PLATFORMS,
                    DEADLINES: ALL_PLATFORMS,
                    AFFILIATE_UPDATES: ALL_PLATFORMS,
                    SCHOOL_SETTINGS: null,
                    CHAT: null,
                    BILLING: null,
                },
            });
        });
    });

    it('Должен получить настройки, если в базе с неполными данными', async () => {
        await prisma.notificationsSettings.create({
            data: {
                userUuid: 'e6d94704-be82-49f7-a352-abdfe199272f',
                version: 2,
                sound: 'VOOP',
                settings: {
                    HOMEWORKS: ['PLATFORM', 'EMAIL'],
                    WITHDRAWAL: ['PLATFORM'],
                    TARIFFS: ['PLATFORM'],
                    SUBSCRIPTION: ['PLATFORM'],
                },
            },
        });
        const session = await createGqlTestSession(app, {
            ...USER_01,
            userId: 'e6d94704-be82-49f7-a352-abdfe199272f',
        });

        const settings = await session.getNotificationSettings('student');
        expect(settings).toEqual({
            notificationsSettings: {
                sound: 'VOOP',
                COURSES: [],
                GROUP_STUDENT_LIMIT_OVER: null,
                COURSE_STUDENT_FINISHED: null,
                WEBINARS: [],
                HOMEWORKS: ['PLATFORM', 'EMAIL'],
                DEADLINES: [],
                AFFILIATE_UPDATES: [],
                SCHOOL_SETTINGS: null,
                CHAT: null,
                BILLING: null,
            },
        });
    });

    // TODO: после миграции на вторую версию, можно удалить
    describe('Миграция на вторую версию', () => {
        let studentSession2: GqlTestSession;
        let teacherSession2: GqlTestSession;

        beforeEach(async () => {
            await prisma.notificationsSettings.createMany({
                data: oldSettings as Prisma.NotificationsSettingsCreateInput[],
            });

            studentSession2 = await createGqlTestSession(app, {
                ...USER_01,
                userId: 'dbb1a050-5724-41ca-9d75-575d753fb19c',
            });
            teacherSession2 = await createGqlTestSession(app, {
                ...USER_01,
                userId: 'dbb1a050-5724-42ca-9d75-575d753fb29c',
            });
        });

        afterAll(async () => {
            await prisma.notificationsSettings.deleteMany();
        });

        it('Должен получить настройки для студента', async () => {
            const result = await studentSession2.getNotificationSettings('student');

            expect(result).toEqual({
                notificationsSettings: {
                    sound: 'VOOP',
                    COURSES: ['PLATFORM'],
                    GROUP_STUDENT_LIMIT_OVER: null,
                    COURSE_STUDENT_FINISHED: null,
                    WEBINARS: ['PLATFORM', 'EMAIL'],
                    HOMEWORKS: ['PLATFORM', 'EMAIL'],
                    DEADLINES: [],
                    AFFILIATE_UPDATES: [],
                    SCHOOL_SETTINGS: null,
                    CHAT: null,
                    BILLING: null,
                },
            });
        });

        it('Должен обновить настройки для студента', async () => {
            const updateInput = {
                sound: 'TODO',
                COURSES: ['PLATFORM', 'BROWSER'],
                WEBINARS: ['PLATFORM', 'EMAIL'],
                HOMEWORKS: ['EMAIL'],
                DEADLINES: ['TELEGRAM', 'EMAIL', 'BROWSER'],
                AFFILIATE_UPDATES: ['TELEGRAM', 'EMAIL', 'BROWSER'],
            };

            await studentSession2.setUserSettings(updateInput, 'student');

            const result = await studentSession2.getNotificationSettings('student');
            expect(result).toEqual({
                notificationsSettings: {
                    sound: 'TODO',
                    COURSES: ['PLATFORM', 'BROWSER'],
                    GROUP_STUDENT_LIMIT_OVER: null,
                    COURSE_STUDENT_FINISHED: null,
                    WEBINARS: ['PLATFORM', 'EMAIL'],
                    HOMEWORKS: ['EMAIL'],
                    DEADLINES: ['TELEGRAM', 'EMAIL', 'BROWSER'],
                    AFFILIATE_UPDATES: ['TELEGRAM', 'EMAIL', 'BROWSER'],
                    SCHOOL_SETTINGS: null,
                    CHAT: null,
                    BILLING: null,
                },
            });
        });

        it('Должен получить настройки для преподавателя', async () => {
            const result = await teacherSession2.getNotificationSettings('teacher');

            expect(result).toEqual({
                notificationsSettings: {
                    sound: 'VOOP',
                    COURSES: null,
                    GROUP_STUDENT_LIMIT_OVER: ['PLATFORM', 'EMAIL'],
                    COURSE_STUDENT_FINISHED: ['PLATFORM', 'EMAIL'],
                    WEBINARS: ['PLATFORM', 'EMAIL'],
                    HOMEWORKS: ['PLATFORM', 'EMAIL'],
                    DEADLINES: null,
                    AFFILIATE_UPDATES: ['PLATFORM', 'EMAIL'],
                    SCHOOL_SETTINGS: ['PLATFORM', 'EMAIL'],
                    CHAT: ['PLATFORM', 'EMAIL'],
                    BILLING: ['PLATFORM', 'EMAIL'],
                },
            });
        });
    });
});
