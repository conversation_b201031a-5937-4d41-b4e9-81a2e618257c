import 'reflect-metadata';

import { LocaleEnum, Prisma } from '@prisma/client';

import * as userSettingsData from '../__data__/notifications-settings.data.json';
import { TestAction } from '../settings-constants';
import { createMessageV2, getUsers } from '../test.utils';
import {
    emailSpy,
    notificationConsumer,
    notificationsResolver,
    prisma,
    telegramSpy,
    userSession1,
    userSession2,
    wsSpy,
} from '../test-setup';

describe('Отправка уведомлений', () => {
    beforeAll(async () => {
        await prisma.notificationsSettings.createMany({
            data: userSettingsData as Prisma.NotificationsSettingsCreateInput[],
        });
    });

    beforeEach(async () => {
        jest.clearAllMocks();
    });

    afterAll(async () => {
        await prisma.notificationsSettings.deleteMany();
    });

    afterEach(async () => {
        await prisma.userNotification.deleteMany();
        await prisma.notification.deleteMany();
    });

    it('Резолвер уведомлений должен быть доступен', () => {
        expect(notificationsResolver).toBeDefined();
    });

    describe('Курсы: Доступ к новому курсу', () => {
        beforeEach(async () => {
            // во время запроса настроек считываем и сохраняем локаль пользователя
            await userSession2.getUserNotifications({ viewed: false }, { skip: 0, take: 100 }, LocaleEnum.en);
            await notificationConsumer.handleNotificationsMessageV2(
                createMessageV2(TestAction.ACTION_COURSE_ACCESS_STUDENT_INVITE, getUsers([1, 2])),
            );
        });

        it.only('Должен уведомить по email на русском языке', async () => {
            expect(emailSpy).toHaveBeenCalledTimes(2);

            expect(emailSpy).toHaveBeenCalledWith({
                schoolUuid: expect.any(String),
                template: 'notification',
                subject: 'Вам открыт доступ к новому курсу',
                fromName: 'Школа',
                fromEmail: '<EMAIL>',
                recipients: ['<EMAIL>'],
                body: {
                    primaryColor: '#17b198',
                    hideMobileAppLinks: true,
                    isWhiteLabel: true,
                    schoolLogoUrl: null,
                    textOnly: false,
                    title: null,
                    html: 'Вам открыт доступ к курсу <b>"Курс молодого сурка"</b>',
                    buttonText: 'Подробнее',
                    redirectUrl: 'https://dev.sksp.site/course/78',
                    unSubscriberUrl: 'https://some.url.com/unsubscribe/1',
                },
            });
        });

        it.only('Должен уведомить по email на английском языке', async () => {
            expect(emailSpy).toHaveBeenCalledTimes(2);

            expect(emailSpy).toHaveBeenCalledWith({
                schoolUuid: expect.any(String),
                template: 'notification',
                subject: "You've been granted access to a new course",
                fromName: 'Школа',
                fromEmail: '<EMAIL>',
                recipients: ['<EMAIL>'],
                body: {
                    primaryColor: '#17b198',
                    hideMobileAppLinks: true,
                    isWhiteLabel: true,
                    schoolLogoUrl: null,
                    textOnly: false,
                    title: null,
                    html: 'You have been invited to the course <b>"Курс молодого сурка"</b>',
                    buttonText: 'Details',
                    redirectUrl: 'https://dev.sksp.site/course/78',
                    unSubscriberUrl: 'https://some.url.com/unsubscribe/2',
                },
            });
        });

        it('Должен уведомить в телеграмм на русском языке', async () => {
            expect(telegramSpy).toHaveBeenCalledTimes(2);

            expect(telegramSpy).toHaveBeenCalledWith({
                text: 'Вам открыт доступ к курсу <b>"Курс молодого сурка"</b> https://dev.sksp.site/course/78',
                unionAuthKey: '6fc112f6-e471-4d83-94c2-ff447f43d5e0',
            });
        });

        it('Должен уведомить в телеграмм на английском языке', async () => {
            expect(telegramSpy).toHaveBeenCalledTimes(2);

            expect(telegramSpy).toHaveBeenCalledWith({
                text: 'You have been invited to the course <b>"Курс молодого сурка"</b> https://dev.sksp.site/course/78',
                unionAuthKey: '6fc112f6-e471-4d83-94c2-ff447f43d5e0',
            });
        });

        it('Должен уведомить на платформе на русском языке', async () => {
            expect(wsSpy).toHaveBeenCalledTimes(2);

            expect(wsSpy).toHaveBeenCalledWith({
                id: expect.any(String),
                userId: 1,
                schoolId: 1,
                userUuid: '6fc112f6-e471-4d83-94c2-ff447f43d5e1',
                schoolUuid: '6fc112f6-e471-4d83-94c2-ff447f43d5e3',
                type: null,
                action: 'ACTION_COURSE_ACCESS_STUDENT_INVITE',
                text: 'Вам открыт доступ к курсу <b>"Курс молодого сурка"</b>',
                meta: {
                    whiteLabel: {
                        primaryColor: '#17b198',
                        hideMobileAppLinks: true,
                        isWhiteLabel: true,
                        schoolLogoUrl: null,
                        textOnly: false,
                    },
                    course: { id: 78, name: 'Курс молодого сурка', uuid: '421c66b4-8f92-4c43-aab7-c4d571dff99b' },
                    domain: 'dev.sksp.site',
                    url: 'https://dev.sksp.site/course/78',
                },
                viewed: false,
                createdAt: expect.any(Number),
                updatedAt: expect.any(Number),
            });
        });

        it('Должен уведомить на платформе на английском языке', async () => {
            expect(wsSpy).toHaveBeenCalledTimes(2);

            expect(wsSpy).toHaveBeenCalledWith({
                id: expect.any(String),
                userId: 2,
                schoolId: 1,
                userUuid: '6fc112f6-e471-4d83-94c2-ff447f43d5e2',
                schoolUuid: '6fc112f6-e471-4d83-94c2-ff447f43d5e3',
                type: null,
                action: 'ACTION_COURSE_ACCESS_STUDENT_INVITE',
                text: 'You have been invited to the course <b>"Курс молодого сурка"</b>',
                meta: {
                    whiteLabel: {
                        primaryColor: '#17b198',
                        hideMobileAppLinks: true,
                        isWhiteLabel: true,
                        schoolLogoUrl: null,
                        textOnly: false,
                    },
                    course: { id: 78, name: 'Курс молодого сурка', uuid: '421c66b4-8f92-4c43-aab7-c4d571dff99b' },
                    domain: 'dev.sksp.site',
                    url: 'https://dev.sksp.site/course/78',
                },
                viewed: false,
                createdAt: expect.any(Number),
                updatedAt: expect.any(Number),
            });
        });
    });

    describe('Домашки: Выполненные задания для преподавателя', () => {
        it('Должен уведомить преподавателя только по email, если сообщение по cron', async () => {
            await notificationConsumer.handleNotificationsMessageV2(
                createMessageV2(TestAction.ACTION_HOMEWORKS_STUDENTS_SENT_CRON, getUsers([1])),
            );

            expect(emailSpy).toHaveBeenCalledTimes(1);
            expect(telegramSpy).toHaveBeenCalledTimes(0);
            expect(wsSpy).toHaveBeenCalledTimes(0);
        });

        it('Не должен сохранять сообщение о выполненных заданиях, если сообщение по cron', async () => {
            await notificationConsumer.handleNotificationsMessageV2(
                createMessageV2(TestAction.ACTION_HOMEWORKS_TEACHER_SENT_CRON, getUsers([1])),
            );

            const result = await userSession1.getUserNotifications({ viewed: false }, { skip: 0, take: 10 });
            expect(result.notifications.length).toEqual(0);
        });

        it('Должен уведомить преподавателя о выполненном задании по всем каналам кроме почты', async () => {
            await notificationConsumer.handleNotificationsMessageV2(
                createMessageV2(TestAction.ACTION_HOMEWORK_STUDENT_SENT, getUsers([1])),
            );

            expect(emailSpy).toHaveBeenCalledTimes(0);
            expect(telegramSpy).toHaveBeenCalledTimes(1);
            expect(wsSpy).toHaveBeenCalledTimes(1);
        });
    });

    describe('Домашки: Проверки заданий для студента', () => {
        it('Должен уведомить студента только по email о проверенных заданиях, если сообщение по cron', async () => {
            await notificationConsumer.handleNotificationsMessageV2(
                createMessageV2(TestAction.ACTION_HOMEWORKS_TEACHER_SENT_CRON, getUsers([1])),
            );

            expect(emailSpy).toHaveBeenCalledTimes(1);
            expect(telegramSpy).toHaveBeenCalledTimes(0);
            expect(wsSpy).toHaveBeenCalledTimes(0);
        });

        it('Не должен сохранять сообщение о проверенных заданиях, если сообщение по cron', async () => {
            await notificationConsumer.handleNotificationsMessageV2(
                createMessageV2(TestAction.ACTION_HOMEWORKS_TEACHER_SENT_CRON, getUsers([1])),
            );

            const result = await userSession1.getUserNotifications({ viewed: false }, { skip: 0, take: 10 });
            expect(result.notifications.length).toEqual(0);
        });

        it('Должен уведомить о проверенном задании по всем каналам кроме почты', async () => {
            await notificationConsumer.handleNotificationsMessageV2(
                createMessageV2(TestAction.ACTION_HOMEWORK_TEACHER_SENT, getUsers([1])),
            );

            expect(emailSpy).toHaveBeenCalledTimes(0);
            expect(telegramSpy).toHaveBeenCalledTimes(1);
            expect(wsSpy).toHaveBeenCalledTimes(1);
        });
    });

    describe('Партнёрка: Подтверждение реквизитов', () => {
        it('Должен уведомить о подтверждении реквизитов партнера', async () => {
            await notificationConsumer.handleNotificationsMessageV2(
                createMessageV2(TestAction.ACTION_AFFILIATE_REQUISITES_ACCEPTED, getUsers([1])),
            );

            expect(emailSpy).toHaveBeenCalledTimes(1);
            expect(telegramSpy).toHaveBeenCalledTimes(1);
            expect(wsSpy).toHaveBeenCalledTimes(1);
        });

        it('Должен сохранить уведомление о подтверждении реквизитов партнера', async () => {
            await notificationConsumer.handleNotificationsMessageV2(
                createMessageV2(TestAction.ACTION_AFFILIATE_REQUISITES_ACCEPTED, getUsers([1])),
            );

            const result = await userSession1.getUserNotifications({ viewed: false }, { skip: 0, take: 10 });
            expect(result.notifications.length).toEqual(1);
        });
    });
});
