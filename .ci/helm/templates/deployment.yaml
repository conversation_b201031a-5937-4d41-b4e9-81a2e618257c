---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}
spec:
  replicas: 3
  selector:
    matchLabels:
      app: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ .Release.Name }}
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configs.yml") . | sha256sum }}
    spec:
      imagePullSecrets:
      - name: registry-credentials
      containers:
      - name: {{ .Release.Name }}
        image: {{ .Values.image }}
        resources:
          requests:
            cpu: "0.4"
            memory: "512Mi"
          limits:
            cpu: "3.0"
            memory: "8Gi"
        volumeMounts:
        - name: configmap-volume
          mountPath: /app/.env
          subPath: .env
      volumes:
      - name: configmap-volume
        configMap:
          name: dotenv-{{ .Release.Name }}-configmap
      hostAliases:
        {{- toYaml .Values.hostAliases | nindent 8 }}
