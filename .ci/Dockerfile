# Используем базовый образ Node.js
FROM node:20-alpine3.20 AS base

# Устанавливаем pnpm
RUN npm install -g pnpm@9.15.4

RUN pnpm config set global-bin-dir /usr/local/bin && \
    pnpm config set prefix /usr/local

# Устанавливаем @nestjs/cli глобально
RUN pnpm install -g @nestjs/cli
RUN npm install -g migrate-mongo@9.0.0

# Устанавливаем необходимые пакеты (например, git)
RUN apk add --no-cache git

# Создаем пользователя и группу
RUN deluser --remove-home node && \
    addgroup -S node -g 1000 && \
    adduser -S -G node -u 1000 node


COPY ./ /app/
WORKDIR /app

# Устанавливаем переменную окружения NODE_ENV
ENV NODE_ENV=production

RUN pnpm install --prod

RUN pnpm build

# Запуск приложения
CMD [ "pnpm", "run", "start"]
