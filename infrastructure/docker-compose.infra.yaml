services:
  redis:
    image: redis:7.0
    container_name: redis
    hostname: redis
    ports:
      - '6379:6379'
    command: ['redis-server', '--appendonly', 'yes']
    volumes:
      - redis-data:/data
    networks:
      - skillspace-network

  rabbitmq:
    image: rabbitmq:3.9-management
    container_name: rabbitmq
    hostname: rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RA<PERSON><PERSON>MQ_DEFAULT_PASS: guest
      RA<PERSON><PERSON>MQ_DEFAULT_VHOST: /
    ports:
      - '5672:5672'
      - '15672:15672'
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq
    networks:
      - skillspace-network
    deploy:
      restart_policy:
        condition: any

networks:
  skillspace-network:
    external: true

volumes:
  redis-data:
  rabbitmq-data:
